@import "~bootstrap/scss/bootstrap";

:root {
  --bs-link-color: var(--bs-body-color);
  --bs-link-hover-color: var(--bs-body-color);
}

.container-adhesion {
  max-width: 680px;
}

.btn-primary {
  --bs-btn-bg: var(--bs-primary);
  --bs-btn-border-color: var(--bs-primary);
  --bs-btn-hover-bg: var(--bs-primary);
  --bs-btn-hover-border-color: var(--bs-primary);
  --bs-btn-active-bg: var(--bs-primary);
  --bs-btn-active-border-color: var(--bs-primary);

  &:hover {
    filter: brightness(0.95);
  }
}

.btn-secondary {
  --bs-btn-bg: var(--bs-secondary);
  --bs-btn-hover-bg: var(--bs-secondary);
  --bs-btn-border-color: var(--bs-secondary);
  --bs-btn-hover-border-color: var(--bs-secondary);
  --bs-btn-active-bg: var(--bs-secondary);
  --bs-btn-active-border-color: var(--bs-secondary);

  &:hover {
    filter: brightness(0.95);
  }
}

@media (min-width: 680px) {
  .card {
    --bs-card-spacer-y: 3rem;
    --bs-card-spacer-x: 3rem;
  }
}
