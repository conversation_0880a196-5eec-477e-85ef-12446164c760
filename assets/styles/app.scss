:root {
  --header-height: 55px;
}

body {
  background-color: #e5e5e5;
  font-family: 'Montserrat', Arial, Helvetica, sans-serif;
  font-size: 14px;
}

.header {
  background-color: #2e3036;
  height: var(--header-height);
  width: 100%;
  z-index: 99;
}

label.required:not(.form-check-label):after, legend.required:after {
  content: "*";
}

hr {
  opacity: 0.1;
  margin: 2rem 0;
}

.form-text a {
  color: #6c757d;
}

/*** Formulaire création enseigne ***/

.options-sorting > div,
.sections-sorting > div,
.fields-sorting > div,
{
  height: 70px;
  overflow: hidden;
}

.options-sorting,
.sections-sorting,
.fields-sorting {
  .handle {
    cursor: grabbing;
  }
}

.handle {
  cursor: grab;
  color: #6c757d;

  &:hover {
    color: #495057;
  }
}

/*** Sommaire ***/

.summary-dot {
  --size: 40px;
  width: var(--size);
  height: var(--size);
  min-width: var(--size);
  min-height: var(--size);
  line-height: var(--size);
  background: var(--bs-primary);
  color: white;
  text-align: center;
  border-radius: 100%;
  font-size: 1.2rem;
  font-weight: bold;
  margin-right: 0.5rem;
}


/*** Signature ***/

.signature-pad-body {
  position: relative;
  width:  100%;
  height: 175px;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#signature-icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 36px;
  text-align: center;
  line-height: 175px;
  width: 100%;

  .fa-pen {
    padding-bottom: 26px;
    font-size: 36px;
    position: absolute;
    bottom: 60px;
    margin-left: -4px;
  }
}

#signature-pad {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height:175px;
  --bs-border-opacity: .25;
  border: var(--bs-border-width) var(--bs-border-style) #dee2e6 !important;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}

/** Formulaire dynamique **/

.fields-sidebar {
  width: 256px;
  background: #fff;
  border-radius: 0.375rem;
  padding: 1rem;
  position: sticky;
  top: 1rem;
  height: calc(100vh - 2rem);
  overflow-y: auto;
}

/* Pour améliorer l'apparence de la scrollbar */
.fields-sidebar::-webkit-scrollbar {
  width: 8px;
}

.fields-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.fields-sidebar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.fields-sidebar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.sidebar-title {
  color: #212529;
  font-size: 1rem;
  font-weight: bold;
}

.field-item {
  padding: 0.5rem 1rem;
  margin-bottom: 0.5rem;
  background: #DADADC;
  color: #616163;
  border-radius: 0.375rem;
  cursor: move;
  transition: all 0.2s;
  font-weight: 600;

  .handle {
    color: #ADADAD;
  }
}

.field-item.disabled {
  cursor: not-allowed;
  opacity: 0.4;
  border: 2px dashed #616163;
  background: #ffffff;
}

.field-item.disabled .handle {
  display: none;
}

.fields-list {
  min-height: 70px;
}

.fields-list:empty {
  border: 2px dashed #DADADC;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.fields-list:empty::after {
  content: "Glissez-déposez des champs ici";
  color: #616163;
  font-style: italic;
}

.field-item:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.field-item.dragging {
  opacity: 0.5;
}

.fields-list.drag-over {
  background-color: rgba(13, 110, 253, 0.1);
  border-radius: 0.375rem;
}

.field-container {
  background: #C2DAFE;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

.field-header {
  color: #616163;
}

.field-header .field-title {
  min-height: 31px;
  display: flex;
  align-items: center;
}

.field-header .field-title input {
  border: none;
  background: transparent;
  font-weight: bold;
  padding: 0;
  margin: 0;
  width: auto;
}

.field-header .field-title input:focus {
  outline: none;
  box-shadow: none;
}

.field-content {
  transition: all 0.3s ease;
}

.field-content.collapsed {
  display: none;
}

.toggle-field i {
  transition: transform 0.3s ease;
}

.toggle-field.collapsed i {
  transform: rotate(-90deg);
}

/* Styles pour Dragula */

.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  filter: alpha(opacity=80);
}
.gu-hide {
  display: none !important;
}
.gu-unselectable {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}
.gu-transit {
  opacity: 0.2;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
  filter: alpha(opacity=20);
}

/* Styles spécifiques pour notre application */
.fields-list.gu-dragula-container {
  min-height: 50px;
  padding: 10px;
  border: 1px dashed #ccc;
}

.handle {
  cursor: move;
  cursor: grab;
}

.handle:active {
  cursor: grabbing;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

/* Styles pour les sections en cours de déplacement */
.dragging-section {
  opacity: 0.9;
  background-color: #f8f9fa;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  z-index: 1000;
}

.section-collapsed {
  transition: height 0.2s ease-in-out;
  height: 66px;
  overflow: hidden;
}

/* Style pour le miroir pendant le drag */
.gu-mirror.dragging-section {
  opacity: 0.8;
  background-color: #e9ecef;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

/** Champ autocompletion ville **/

.geo-results {
  position: absolute;
  z-index: 1000;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 2px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.geo-result-item {
  padding: 8px 12px;
  cursor: pointer;
}

.geo-result-item:hover {
  background-color: #f8f9fa;
}

.geo-autocomplete-wrapper {
  position: relative;
}
