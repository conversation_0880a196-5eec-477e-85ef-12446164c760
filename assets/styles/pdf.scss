$gutter: 22px;
$delimiter: 2px;

body {
  font-family: sans-serif;
}

.page {
  page-break-after: always;
  padding: 0 $gutter * 2;
}

.page.page-no-break {
  page-break-after: avoid;
}

.no-break {
  page-break-inside: avoid;
}

.delimiter {
  margin-top: $gutter * 2;
  margin-bottom: $gutter;
  border-top-width: $delimiter;
  border-top-style: solid;
}

.inner {
  padding: 0 $gutter * 2;
  font-size: 18px;
}

h1, h2, h3 {
  text-transform: uppercase;
}

h1 {
  margin-top: 300px;
  border-bottom-width: $delimiter;
  border-bottom-style: solid;
  padding-bottom: calc($gutter / 2);
  font-size: 32px;
}

h2 {
  width: 100%;
  border-bottom-width: $delimiter;
  border-bottom-style: solid;
  padding-bottom: $gutter;
  margin-bottom: $gutter;
}

h3 {
  padding-bottom: $gutter;
}

.label {
  font-weight: 600;
}

.row {
  display: -webkit-box; /* wkhtmltopdf uses this one */
  display: flex;
  -webkit-box-pack: center; /* wkhtmltopdf uses this one */
  justify-content: center;
}

.row > div {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

.row > div:last-child {
  margin-right: 0;
}