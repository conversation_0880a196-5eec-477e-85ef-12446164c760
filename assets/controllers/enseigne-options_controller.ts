import { Controller } from '@hotwired/stimulus';
import Sortable from "sortablejs";

export default class extends Controller {
    connect() {
        this.initializeSortables();

        // Réinitialiser les sortables après chaque mise à jour des collections
        addEventListener('update-collection', () => {
            this.initializeSortables();
        });
    }

    private initializeSortables() {
        // Gestion du drag and drop des options
        const optionsList = document.getElementById('options-list');
        this.initSortable(optionsList, 'options-sorting');
    }

    private initSortable(element: HTMLElement, sortingClass: string) {
        if (!element) return;

        // Détruire l'instance Sortable existante si elle existe
        const existingSortable = Sortable.get(element);
        if (existingSortable) {
            existingSortable.destroy();
        }

        new Sortable(element, {
            handle: '.handle',
            animation: 150,
            onStart: () => {
                element.classList.add(sortingClass);
            },
            onEnd: (evt) => {
                element.classList.remove(sortingClass);
                this.updatePositions();
            }
        });

        this.updatePositions();
    }

    private updatePositions() {
        const optionsList = document.getElementById('options-list');
        if (!optionsList) return;

        const items = Array.from(optionsList.children);
        items.forEach((node, index) => {
            const positionInput = node.querySelector('.position') as HTMLInputElement;
            if (positionInput) {
                positionInput.value = index.toString();
            }
        });
    }
}