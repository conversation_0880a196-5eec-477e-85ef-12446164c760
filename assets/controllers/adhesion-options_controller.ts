import { Controller } from '@hotwired/stimulus';
import Sortable from "sortablejs";

export default class extends Controller {

	connect() {
		Array.from(document.querySelectorAll('[data-price]'), (input: HTMLInputElement) => {
			let price = parseFloat(input.dataset.price);
			let priceNode = document.getElementById(`price-${input.dataset.id}`);

            if (priceNode) {
                input.addEventListener('change', () => {
                    let newPrice = parseInt(input.value) > 0 ? parseInt(input.value) * price : 0;
                    newPrice = Math.round(newPrice * 100) / 100;
                    priceNode.innerText = `${newPrice}€ HT`;
                })
            }

			input.dispatchEvent(new Event('change'));
		})
	}

}