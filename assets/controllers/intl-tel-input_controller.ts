import { Controller } from '@hotwired/stimulus';
import intlTelInput from 'intl-tel-input';
import 'intl-tel-input/build/css/intlTelInput.css';

export default class extends Controller {
    static targets = ['input', 'hidden'];
    static values = {
        onlyCountries: { type: Array, default: [] },
    };

    connect() {

        let countriesDialCode = this.onlyCountriesValue.map(c => c.replace('+', ''));

        let onlyCountries = intlTelInputGlobals.getCountryData().filter(c => countriesDialCode.includes(c.dialCode)).flatMap((c) => c.iso2);

        let iti = intlTelInput(this.inputTarget, {
            initialCountry: 'fr',
            separateDialCode: true,
            showSelectedDialCode: true,
            formatOnDisplay: false,
            onlyCountries: onlyCountries,
            allowDropdown: true,
            validationNumberTypes: "FIXED_LINE_OR_MOBILE",
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/19.2.16/js/utils.js"
        });

        this.inputTarget.addEventListener('change', () => {
            this.hiddenTarget.value = iti.getNumber();
        })
    }
}
