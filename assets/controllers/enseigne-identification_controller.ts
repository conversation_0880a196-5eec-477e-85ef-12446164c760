import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    connect() {
        /** Gestion de la preview du logo **/
        let logoInput = <HTMLInputElement>document.getElementById("enseigne_logoFile");
        let logoPreview = <HTMLImageElement>document.getElementById("logo-preview");

        logoInput?.addEventListener('change', (event) => {
            logoPreview.parentElement.classList.remove("d-none");
            logoPreview.src = URL.createObjectURL((<HTMLInputElement>event.target).files[0]);
        });
    }
}