import {Controller} from "@hotwired/stimulus";
import dragula from "dragula";
import type FormCollectionController from './form-collection_controller';

// Interfaces pour améliorer le typage
interface FieldConfig {
    type: string;
    position?: number;
    [key: string]: any;
}

interface FieldTypeConfig {
    icon?: string;
    label?: string;
    [key: string]: any;
}

export default class extends Controller {
    private dragulaInstances: dragula.Drake[] = [];
    private sidebarDrake: dragula.Drake;
    private usedFormattedFields: Set<string> = new Set();

    connect() {
        // Initialiser les composants de drag-and-drop
        this.initializeDragula();
        this.initializeSidebarDragula();
        this.updateFormattedFieldsAvailability();
        this.initializeFieldTitles();

        // Configurer les écouteurs d'événements
        addEventListener("update-collection", this.handleUpdateCollection);
        addEventListener("new-collection", this.handleUpdateCollection);
        addEventListener("formatted-field-removed", this.handleFormattedFieldRemoved);
    }

    toggleField(event: Event) {
        const button = event.currentTarget as HTMLElement;
        const fieldContainer = button.closest(".field-container");
        const content = fieldContainer.querySelector(".field-content");

        button.classList.toggle("collapsed");
        content.classList.toggle("collapsed");
    }

    private initializeFieldTitles() {
        document.querySelectorAll(".field-container").forEach((container: HTMLElement) => {
            const typeInput = container.querySelector('[name$="[type]"]') as HTMLInputElement;
            const fieldTitle = container.querySelector('.field-title') as HTMLElement;

            if (typeInput && fieldTitle) {
                const iconClass = this.getFieldTypeConfig(typeInput.value)?.icon;
                const fieldName = this.getFieldTypeConfig(typeInput.value)?.label;

                if (iconClass) {
                    fieldTitle.querySelector('i').classList.add(...(iconClass ?? '').split(' '));
                }
                fieldTitle.querySelector('span').textContent = fieldName ?? '';
            }
        });
    }

    private getFieldTypeConfig(fieldType: string): FieldTypeConfig {
        // Chercher dans la sidebar le champ avec le même type
        const sidebarItems = document.querySelectorAll('.fields-sidebar .field-item');

        for (let item of sidebarItems) {
            const fieldConfig = JSON.parse((item as HTMLElement).dataset.fieldConfig || '{}');
            const fieldAllConfig = JSON.parse((item as HTMLElement).dataset.fieldAllConfig || '{}');
            if (fieldConfig.type === fieldType) {
                return fieldAllConfig;
            }
        }

        return null;
    }

    private initializeDragula() {
        // Détruire les instances existantes
        this.dragulaInstances.forEach(drake => drake.destroy());
        this.dragulaInstances = [];

        this.initializeSectionsDragula();
        this.initializeFieldsDragula();
    }

    private initializeSectionsDragula() {
        // Gestion du drag and drop des sections
        const sectionsList = document.getElementById("sections-list");
        if (!sectionsList) {
            console.warn('Sections list not found');
            return;
        }

        const sectionsDrake = dragula([sectionsList], {
            moves: this.canMoveSection,
            mirrorContainer: document.body
        });

        sectionsDrake.on('drag', this.handleSectionDragStart.bind(this, sectionsList));
        sectionsDrake.on('drop', this.handleSectionDrop.bind(this, sectionsList));
        sectionsDrake.on('cancel', this.handleSectionDragCancel.bind(this, sectionsList));

        this.dragulaInstances.push(sectionsDrake);
    }

    private canMoveSection(el: Element, container: Element, handle: Element): boolean {
        // Vérifier si le handle est le handle principal de la section (premier niveau)
        const sectionHandle = el.querySelector(':scope > .handle');
        return !!(sectionHandle && (sectionHandle === handle || sectionHandle.contains(handle)));
    }

    private handleSectionDragStart(sectionsList: HTMLElement, el: Element) {
        // Collapse toutes les sections pour faciliter le déplacement
        Array.from(sectionsList.children).forEach((section: HTMLElement) => {
            this.collapseSection(section);
        });

        // Ajouter une classe pour le style pendant le drag
        el.classList.add('dragging-section');
    }

    private handleSectionDrop(sectionsList: HTMLElement, el: Element, target: Element, source: Element, sibling: Element) {
        // Expand toutes les sections après le dépôt
        Array.from(sectionsList.children).forEach((section: HTMLElement) => {
            this.expandSection(section);
        });

        // Retirer la classe de style
        el.classList.remove('dragging-section');

        // Mettre à jour les positions
        this.updatePositions(sectionsList);
    }

    private handleSectionDragCancel(sectionsList: HTMLElement, el: Element) {
        // Expand toutes les sections si le drag est annulé
        Array.from(sectionsList.children).forEach((section: HTMLElement) => {
            this.expandSection(section);
        });

        // Retirer la classe de style
        el.classList.remove('dragging-section');
    }

    private initializeFieldsDragula() {
        // Gestion du drag and drop des champs dans chaque section
        const fieldsLists = Array.from(document.querySelectorAll('.fields-list[data-form-collection-target="fields"]'));

        if (fieldsLists.length === 0) {
            console.warn('No fields lists found');
            return;
        }

        // Créer une seule instance Dragula pour toutes les listes de champs
        const fieldsDrake = dragula(fieldsLists as Element[], {
            moves: this.canMoveField,
            mirrorContainer: document.body
        });

        fieldsDrake.on('drop', (el, target, source) => {
            if (target) {
                this.updatePositions(target as HTMLElement);

                // Check if the field was moved to a different section
                if (source !== target) {
                    this.updateFieldSection(el as HTMLElement, target as HTMLElement);
                }
            }
        });

        this.dragulaInstances.push(fieldsDrake);
    }

    private canMoveField(el: Element, container: Element, handle: Element): boolean {
        // Ne pas permettre de déplacer les éléments de la sidebar
        if (el.closest('.fields-sidebar')) {
            return false;
        }

        // Vérifier si c'est un champ (field-container)
        if (!el.classList.contains('field-container')) {
            return false;
        }

        // Vérifier si le handle est le handle du champ
        const fieldHandle = el.querySelector(':scope > .field-header .handle');
        return !!(fieldHandle && (fieldHandle === handle || fieldHandle.contains(handle)));
    }

    private initializeSidebarDragula() {
        // Détruire l'instance existante si elle existe
        if (this.sidebarDrake) {
            this.sidebarDrake.destroy();

            // Array.from(document.querySelectorAll('.fields-list[data-form-collection-target="fields"]')).forEach(fieldList => {
            //     this.sidebarDrake.containers.push(fieldList);
            // });
            //
            // console.log(this.sidebarDrake.containers);
            //
            // return;
        }

        // Récupérer les listes de la sidebar et les listes de champs
        const sidebarLists = Array.from(document.querySelectorAll(".fields-sidebar > .fields-list"));
        const fieldsLists = Array.from(document.querySelectorAll('.fields-list[data-form-collection-target="fields"]'));

        // Créer une instance Dragula pour la sidebar et les listes de champs
        this.sidebarDrake = dragula([...sidebarLists, ...fieldsLists], {
            copy: (el, source) => sidebarLists.includes(source as HTMLElement),
            accepts: (el, target) => {
                return fieldsLists.includes(target as HTMLElement) &&
                    !sidebarLists.includes(target as HTMLElement);
            },
            moves: (el, source) => {
                if (el.classList.contains('disabled')) return false;
                return sidebarLists.includes(source as HTMLElement);
            },
            mirrorContainer: document.body
        });

        // Gérer l'ajout d'un élément depuis la sidebar
        this.sidebarDrake.on('drop', this.handleSidebarDrop.bind(this));
    }

    private handleSidebarDrop(el: HTMLElement, target: HTMLElement, source: HTMLElement, sibling: HTMLElement) {
        // Si l'élément est lâché en dehors d'un conteneur valide, le supprimer
        if (!target) {
            el.remove();
            return;
        }

        // Si l'élément n'a pas de configuration, le supprimer
        if (!el.dataset.fieldConfig) {
            el.remove();
            return;
        }

        const fieldConfig = JSON.parse(el.dataset.fieldConfig) as FieldConfig;
        const allConfig = this.getFieldTypeConfig(fieldConfig.type);
        let formAttributes = {};

        if (allConfig.required) {
            formAttributes = {
                required: {
                    disabled: true
                }
            }
        }

        // Vérifier si c'est un champ formaté déjà utilisé
        if (this.isFormattedField(fieldConfig.type) && this.usedFormattedFields.has(fieldConfig.type)) {
            el.remove();
            return;
        }

        try {
            // Déterminer la position où l'élément a été déposé
            const position = sibling
                ? Array.from(target.children).indexOf(sibling)
                : target.children.length;

            // Ajouter la position à la configuration
            fieldConfig.position = position;

            // Ajouter le champ via form-collection
            this.addField(target as HTMLElement, fieldConfig, formAttributes);
        } catch (error) {
            console.error('Erreur lors du traitement du drop:', error);
        } finally {
            // Supprimer l'élément cloné car il sera recréé par form-collection
            el.remove();
        }
    }

    private addField(list: HTMLElement, fieldConfig: FieldConfig, formAttributes = {}) {
        // Récupérer le prototype du champ depuis le contrôleur form-collection
        const collectionController =
            this.application.getControllerForElementAndIdentifier(
                list.closest('[data-controller="form-collection"]'),
                "form-collection"
            ) as FormCollectionController;

        if (collectionController) {
            collectionController.addItem(fieldConfig, formAttributes);

            // Mettre à jour la disponibilité des champs formatés après l'ajout
            if (this.isFormattedField(fieldConfig.type)) {
                this.updateFormattedFieldsAvailability();
            }
        }
    }

    private updatePositions(fieldsList: HTMLElement) {
        const items = Array.from(fieldsList.children);
        items.forEach((node, index) => {
            const positionInput = node.querySelector(".position") as HTMLInputElement;
            if (positionInput) {
                positionInput.value = index.toString();
            }
        });
    }

    private updateAllPositions() {
        const sectionList = document.getElementById('sections-list');
        if (sectionList) {
            this.updatePositions(sectionList);
        }

        const fieldsLists = document.querySelectorAll(
            '.fields-list[data-form-collection-target="fields"]'
        );

        fieldsLists.forEach((fieldsList: HTMLElement) => {
            this.updatePositions(fieldsList);
        });
    }

    private updateFormattedFieldsAvailability() {
        this.collectUsedFormattedFields();
        this.updateSidebarFieldsAppearance();
    }

    private collectUsedFormattedFields() {
        // Réinitialiser la liste des champs utilisés
        this.usedFormattedFields.clear();

        // Collecter tous les types de champs formatés déjà utilisés
        document.querySelectorAll('.field-container [name$="[type]"]').forEach((input: HTMLInputElement) => {
            const fieldType = input.value;
            const fieldItem = input.closest('.field-container');

            if (fieldItem && this.isFormattedField(fieldType)) {
                this.usedFormattedFields.add(fieldType);
            }
        });
    }

    private updateSidebarFieldsAppearance() {
        // Mettre à jour l'apparence des éléments dans la sidebar
        document.querySelectorAll('.fields-sidebar .fields-list.formatted .field-item').forEach((item: HTMLElement) => {
            const fieldConfig = JSON.parse(item.dataset.fieldConfig || '{}');
            const isDisabled = this.usedFormattedFields.has(fieldConfig.type);

            item.classList.toggle('disabled', isDisabled);
            item.setAttribute('draggable', isDisabled ? 'false' : 'true');
        });
    }

    private isFormattedField(fieldType: string): boolean {
        // Vérifier si le type de champ existe dans la sidebar avec data-field-type="formatted"
        const formattedFieldItems = document.querySelectorAll('.fields-sidebar .field-item[data-field-type="formatted"]');

        return Array.from(formattedFieldItems).some(item => {
            const fieldConfig = JSON.parse((item as HTMLElement).dataset.fieldConfig || '{}');
            return fieldConfig.type === fieldType;
        });
    }

    private collapseSection(sectionElement: HTMLElement): void {
        const sectionContent = sectionElement.querySelector(':scope > .flex-grow-1');
        if (sectionContent) {
            sectionContent.classList.add('section-collapsed');
        }
    }

    private expandSection(sectionElement: HTMLElement): void {
        const sectionContent = sectionElement.querySelector(':scope > .flex-grow-1');
        if (sectionContent) {
            sectionContent.classList.remove('section-collapsed');
        }
    }

    disconnect() {
        // Nettoyer les instances de dragula pour éviter les fuites mémoire
        this.dragulaInstances.forEach(drake => drake.destroy());
        if (this.sidebarDrake) {
            this.sidebarDrake.destroy();
        }

        // Supprimer les écouteurs d'événements
        removeEventListener("update-collection", this.handleUpdateCollection);
        removeEventListener("formatted-field-removed", this.handleFormattedFieldRemoved);
    }

    // Méthodes pour les gestionnaires d'événements
    private handleUpdateCollection = () => {
        setTimeout(() => {
            this.initializeFieldTitles();
            this.updateAllPositions();
            this.initializeDragula();
            this.initializeSidebarDragula();
            this.updateFormattedFieldsAvailability();
        }, 0); // Nécessaire sinon les écouteurs d'évènements de dragula font nimp
    }

    private handleFormattedFieldRemoved = () => {
        this.updateFormattedFieldsAvailability();
    }

    /**
     * Modifie l'id de section d'un champ pour le déplacer formulairement parlant
     */
    private updateFieldSection(fieldElement: HTMLElement, newSectionFieldsList: HTMLElement): void {
        const newSectionElement = newSectionFieldsList.closest('[data-form-collection-target="field"]');

        // Récupération de l'id de la nouvelle section
        const sectionInput = newSectionElement.querySelector('input[name]') as HTMLInputElement;
        const sectionIndex = sectionInput.name.match(/\[sections\]\[(\d+)\]/)[1];

        // Récupération de l'id actuelle du champ
        const fieldInput = fieldElement.querySelector('input[name]') as HTMLInputElement;
        const fieldIndex = fieldInput.name.match(/\[fields\]\[(\d+)\]/)[1];

        // Mise à jour de tous les inputs du champ
        fieldElement.querySelectorAll('input, select, textarea').forEach((input: HTMLInputElement) => {
            input.name = input.name.replace(/\[sections\]\[(\d+)\]\[fields\]\[(\d+)\]/, `[sections][${sectionIndex}][fields][${fieldIndex}]`);
        });
    }
}
