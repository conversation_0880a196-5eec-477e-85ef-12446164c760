import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    connect() {
        /** Gestion de la preview des couleurs **/
        const color1 = document.getElementById('enseigne_customization_color1');
        const color1Preview = document.getElementById('color-1-preview');
        const color2 = document.getElementById('enseigne_customization_color2');
        const color2Preview = document.getElementById('color-2-preview');
        const color3 = document.getElementById('enseigne_customization_color3');
        const color3Preview = document.getElementById('color-3-preview');

        color1?.addEventListener('keyup', (event) => {
            color1Preview.style.background = /^#[0-9A-F]{6}$/i.test((<HTMLInputElement>event.target).value) ? (<HTMLInputElement>event.target).value : '#FFFFFF';
        });

        color2?.addEventListener('keyup', (event) => {
            color2Preview.style.background = /^#[0-9A-F]{6}$/i.test((<HTMLInputElement>event.target).value) ? (<HTMLInputElement>event.target).value : '#FFFFFF';
        });

        color3?.addEventListener('keyup', (event) => {
            color3Preview.style.background = /^#[0-9A-F]{6}$/i.test((<HTMLInputElement>event.target).value) ? (<HTMLInputElement>event.target).value : '#FFFFFF';
        });
    }
}