import { Controller } from '@hotwired/stimulus';

interface Choice {
    label: string;
    value: string;
}

interface TelFieldOptions {
    dialCodes: string[];
}

export default class extends Controller {
    static targets = ['typeSelect', 'optionsInput', 'telOptionsContainer'];

    declare readonly typeSelectTarget: HTMLSelectElement;
    declare readonly optionsInputTarget: HTMLInputElement;
    declare readonly telOptionsContainerTarget: HTMLElement;

    connect() {
        // Initialiser les options si elles existent déjà
        const existingOptions = this.optionsInputTarget.value;
        if (existingOptions && existingOptions !== '[object Object]' && this.telOptionsContainerTarget.children.length === 0) {
            const options: TelFieldOptions = JSON.parse(existingOptions);
            if (options.dialCodes) {
                this.addInputElement(options.dialCodes.join(','));
            }
        }

        // Vérifier si le type actuel nécessite des choix
        this.onTypeChange();
    }

    onTypeChange() {
        const isTelType = this.isTelType(this.typeSelectTarget.value);
        if (!isTelType) {
            return;
        }

        this.telOptionsContainerTarget.classList.toggle('d-none', !isTelType);

        if (isTelType && this.telOptionsContainerTarget.children.length === 0) {
            this.addChoice();
        }

        this.updateOptionsInput();
    }

    addChoice(event?: Event) {
        if (event) {
            event.preventDefault();
        }
        this.addInputElement();
    }

    addInputElement(label: string = '') {
        const choiceDiv = document.createElement('div');
        choiceDiv.className = 'tel-options d-flex align-items-center gap-2 mb-2';
        choiceDiv.innerHTML = `
            <input type="text" class="form-control" placeholder="Indicatifs" value="${label}" data-action="input->field-tel-options#updateOptionsInput">
        `;
        this.telOptionsContainerTarget.appendChild(choiceDiv);
        this.updateOptionsInput();
    }

    updateOptionsInput() {
        const type = this.typeSelectTarget.value;
        if (!this.isTelType(type)) {
            this.optionsInputTarget.value = '';
            return;
        }

        let dialCodes: string[] = [];
        this.telOptionsContainerTarget.querySelectorAll('.tel-options').forEach((item: HTMLElement) => {
            dialCodes = item.querySelector('input').value?.split(',') ?? [];
        });

        const options: TelFieldOptions = {
            dialCodes
        };

        this.optionsInputTarget.value = JSON.stringify(options);
    }

    isTelType(type: string): boolean {
        return ['tel'].includes(type);
    }
}