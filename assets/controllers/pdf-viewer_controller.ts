import { Controller } from '@hotwired/stimulus';
import PDFJSExpress from '@pdftron/pdfjs-express-viewer'

export default class extends Controller {

    static values = {
        pdfUrl: String,
    }

	connect() {
		PDFJSExpress({
            path: '/build/pdfjsexpress',
            licenseKey: process.env.PDFJS_LICENSE_KEY,
			// @ts-ignore
			initialDoc: this.pdfUrlValue,
        }, this.element)
            .then(instance => {
                instance.UI.disableElements(['selectToolButton', 'viewControlsButton', 'leftPanelButton', 'searchButton', 'menuButton', 'moreButton', 'textSelectButton']);

                instance.UI.setZoomLevel(1);

                instance.UI.setHeaderItems(function(header) {
                    header.getHeader('View').push({
                        type: 'actionButton',
                        img: 'icon-header-full-screen',
                        dataElement: 'fullScreenButton',
                        title: 'action.enterFullscreen',
                        onClick: instance.UI.toggleFullScreen
                    });
                });

                instance.UI.setLanguage('fr');
            })

	}

}