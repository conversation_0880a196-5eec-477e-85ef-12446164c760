import { Controller } from '@hotwired/stimulus';

export default class extends Controller {

	static targets = [ 'fields', 'field', 'addButton' ]
	static values = {
		prototype: String,
		maxItems: Number,
		itemsCount: Number,
		placeholder: { type: String, default: "__name__" }
	}

	connect() {
		this.index = this.itemsCountValue = this.fieldTargets.length
	}

	addItem(formValues = {}, formAttributes = {}) {
		let prototype = JSON.parse(this.prototypeValue)
		const newField = prototype.replace(new RegExp(this.placeholderValue, "g"), this.index)

		// Check if a specific position is requested
		const position = formValues.position !== undefined ? parseInt(formValues.position) : -1;

		if (position >= 0 && position < this.fieldsTarget.children.length) {
			// Insert at the specified position
			const referenceNode = this.fieldsTarget.children[position];
			const tempDiv = document.createElement('div');
			tempDiv.innerHTML = newField;
			const newNode = tempDiv.firstElementChild;
			this.fieldsTarget.insertBefore(newNode, referenceNode);
		} else {
			// Insert at the end (default behavior)
			this.fieldsTarget.insertAdjacentHTML('beforeend', newField);
		}

		if (!(formValues instanceof Event) && Object.keys(formValues).length > 0) {
			const newEl = this.fieldsTarget.querySelector(':scope > [data-index="' + this.index + '"]');
			for (const key in formValues) {
				if (key === 'position') continue;

				let inputEl = newEl.querySelector('[name$="[' + key + ']"]');
				if (inputEl) {
					if (inputEl.type === 'checkbox') {
						inputEl.checked = formValues[key];
					} else {
						inputEl.value = formValues[key]
					}
				}

				if (key in formAttributes) {
					for (const attributeName in formAttributes[key]) {
						inputEl[attributeName] = formAttributes[key][attributeName];
					}
				}
			}
		}

		this.index++
		this.itemsCountValue++
		this.triggerUpdateEvent()
	}

	removeItem(event) {
		if (!confirm("Êtes-vous sur de vouloir supprimer l'option ?")) {
			return;
		}

		this.fieldTargets.forEach(element => {
			if (element.contains(event.target)) {
				element.remove()
				this.itemsCountValue--
				window.dispatchEvent(new CustomEvent("field-removed"))
			}
		})

		this.triggerUpdateEvent()
	}

	itemsCountValueChanged() {
		if (false === this.hasAddButtonTarget || 0 === this.maxItemsValue) {
			return
		}
		const maxItemsReached = this.itemsCountValue >= this.maxItemsValue
		this.addButtonTarget.classList.toggle('hidden', maxItemsReached)
	}

	triggerUpdateEvent() {
		window.dispatchEvent(new CustomEvent("update-collection"))
	}

}