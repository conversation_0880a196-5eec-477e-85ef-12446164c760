import { Controller } from '@hotwired/stimulus';

/**
 * Contrôleur pour l'autocomplétion des villes françaises via l'API Adresse
 */
export default class extends Controller {
    static targets = ['input', 'results'];
    static values = {
        url: { type: String, default: 'https://api-adresse.data.gouv.fr/search/' },
        type: { type: String, default: 'municipality' }, // municipality pour les villes uniquement
        debounce: { type: Number, default: 300 } // temps de debounce en ms
    };

    initialize() {
        this.debounceTimer = null;
        this.debouncedFetchResults = this.debounce(this.fetchResults.bind(this), this.debounceValue);
    }

    connect() {
        this.resultsTarget.classList.add('d-none');
        this.inputTarget.addEventListener('input', this.onInput.bind(this));
        this.inputTarget.addEventListener('focus', this.onFocus.bind(this));
        document.addEventListener('click', this.onDocumentClick.bind(this));
    }

    disconnect() {
        this.inputTarget.removeEventListener('input', this.onInput.bind(this));
        this.inputTarget.removeEventListener('focus', this.onFocus.bind(this));
        document.removeEventListener('click', this.onDocumentClick.bind(this));

        // Annuler tout timer en cours
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
    }

    // Fonction de debounce pour limiter les appels à l'API
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                func.apply(this, args);
            }, wait);
        };
    }

    onInput(event) {
        const query = event.target.value.trim();
        if (query.length < 3) {
            this.resultsTarget.classList.add('d-none');
            return;
        }

        this.debouncedFetchResults(query);
    }

    onFocus(event) {
        const query = event.target.value.trim();
        if (query.length >= 3) {
            this.debouncedFetchResults(query);
        }
    }

    onDocumentClick(event) {
        if (!this.element.contains(event.target)) {
            this.resultsTarget.classList.add('d-none');
        }
    }

    async fetchResults(query) {
        try {
            const params = new URLSearchParams({
                q: query,
                limit: 5,
                type: this.typeValue
            });

            const response = await fetch(`${this.urlValue}?${params.toString()}`);
            const data = await response.json();

            this.displayResults(data.features);
        } catch (error) {
            console.error('Erreur lors de la récupération des résultats:', error);
        }
    }

    displayResults(features) {
        if (!features || features.length === 0) {
            this.resultsTarget.classList.add('d-none');
            return;
        }

        this.resultsTarget.innerHTML = '';
        features.forEach(feature => {
            const item = document.createElement('div');
            item.classList.add('geo-result-item');
            item.textContent = feature.properties.label;
            item.dataset.action = 'click->geo-autocomplete#selectResult';
            item.dataset.name = feature.properties.name;
            this.resultsTarget.appendChild(item);
        });

        this.resultsTarget.classList.remove('d-none');
    }

    selectResult(event) {
        const selectedItem = event.currentTarget;
        this.inputTarget.value = selectedItem.dataset.name;
        this.resultsTarget.classList.add('d-none');
    }
}
