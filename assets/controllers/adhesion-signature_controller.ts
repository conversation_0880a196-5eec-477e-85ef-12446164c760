import { Controller } from '@hotwired/stimulus';
import SignaturePad from "signature_pad";

export default class extends Controller {

	connect() {
		const canvas = document.querySelector("canvas");
		const icon = document.getElementById("signature-icon");
		const signatureData = <HTMLInputElement>document.getElementById("adhesion_signature_signatureData");
		const signaturePad = new SignaturePad(canvas);

		document.getElementById("signature-clear").addEventListener("click", () => {
			signaturePad.clear();
			signatureData.value = "";
			icon.classList.remove("d-none");
		})

		signaturePad.addEventListener("beginStroke", () => {
			icon.classList.add("d-none");
		});

		signaturePad.addEventListener("endStroke", () => {
			signatureData.value = signaturePad.toDataURL();
		});

		// @see https://github.com/szimek/signature_pad#handling-high-dpi-screens
		function resizeCanvas() {
			const ratio =  Math.max(window.devicePixelRatio || 1, 1);
			canvas.width = canvas.offsetWidth * ratio;
			canvas.height = canvas.offsetHeight * ratio;
			canvas.getContext("2d").scale(ratio, ratio);
			signaturePad.clear(); // otherwise isEmpty() might return incorrect value
		}

		window.addEventListener("resize", resizeCanvas);
		resizeCanvas();

		if (signatureData.value) {
			signaturePad.fromDataURL(signatureData.value);
			icon.classList.add("d-none");
		}

		//

		const form = document.querySelector('form[name=adhesion_signature]');
		form.addEventListener('submit', () => {
			setTimeout(() => {
				(<HTMLInputElement>form.querySelector("input[type=submit]")).value = "Génération du contrat...";
			}, 500);
		});
	}

}