import { Controller } from '@hotwired/stimulus';
import Inputmask from "inputmask";

export default class extends Controller {

    static values = {
        pattern: String,
    }

    connect() {
        Inputmask.extendDefaults({
            validationEventTimeOut: 30000,
        });

        Inputmask.extendDefinitions({
            'M': {
                validator: "[A-Z0-9]",
                casing: "upper" //auto uppercasing
            },
        });

        // @ts-ignore
        Inputmask(this.patternValue).mask(this.element);
    }
}