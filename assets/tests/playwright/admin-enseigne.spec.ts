import { test, expect, Page } from "@playwright/test";
import { readFileSync } from 'fs';
import path from 'path';

// Données de test pour la création d'une enseigne
const enseigneData = {
  identification: {
    name: "Programme Test Playwright",
    identifier: "TESTPLAY",
    pole: "sante"
  },
  options: [
    {
      name: "Option de base",
      description: "Description de l'option de base",
      unitPrice: "0",
      isFree: true
    },
    {
      name: "Option premium",
      description: "Description de l'option premium",
      unitPrice: "100",
      isFree: false
    }
  ],
  customization: {
    textPresentation: "Texte de présentation du programme",
    textUnderLGO: "Texte sous le logo",
    textBeforeSignature: "Texte avant signature",
    textAfterSignature: "Texte après signature",
    textContact: "Texte de contact",
    color1: "#FF5733",
    color2: "#33FF57",
    color3: "#3357FF",
    linkMobilityAccp: "AGSI01_123456789"
  },
  facturation: {
    askForSepa: true
  },
  form: {
    sections: [
      {
        title: "Informations générales",
        description: "Informations de base",
        fields: [
          {
            type: "managerEmail",
            required: true,
            label: "Adresse e-mail du responsable"
          },
          {
            type: "finess",
            required: true,
            label: "Numéro FINESS"
          },
          {
            type: "siret",
            required: true,
            label: "SIRET"
          }
        ]
      },
      {
        title: "Coordonnées",
        description: "Adresse et contact",
        fields: [
          {
            type: "text",
            required: true,
            label: "Adresse"
          },
          {
            type: "text",
            required: true,
            label: "Code postal"
          },
          {
            type: "city",
            required: true,
            label: "Ville"
          }
        ]
      }
    ]
  },
  notifications: {
    contractFilename: "contrat-test",
    emailSenderEmail: "<EMAIL>",
    emailSenderName: "Test Playwright",
    emailAquitemEnabled: true,
    emailAquitemAttachContract: true,
    emailAquitemRecipientEmail: "<EMAIL>",
    emailAquitemSubject: "Nouvelle adhésion",
    emailAquitemContent: "Contenu de l'email pour Aquitem",
    emailEnseigneEnabled: true,
    emailEnseigneAttachContract: true,
    emailEnseigneRecipientEmail: "<EMAIL>",
    emailEnseigneSubject: "Nouvelle adhésion pour l'enseigne",
    emailEnseigneContent: "Contenu de l'email pour l'enseigne",
    emailSubscriberEnabled: true,
    emailSubscriberAttachContract: true,
    emailSubscriberSubject: "Votre adhésion",
    emailSubscriberContent: "Contenu de l'email pour l'adhérent"
  }
};

let cookieName = "test_env";
let cookieValue = process.env.APP_SECRET ?? "";

test.beforeEach(async ({ page, baseURL }) => {
  await page.context().clearCookies();
  await page.goto("/");
  // Ajout du cookie permettant de passer au travers des captchas
  await page.context().addCookies([
    {
      name: cookieName,
      value: cookieValue,
      url: baseURL,
    },
  ]);
});

test("Création d'une nouvelle enseigne", async ({ page }) => {
  // Accéder à la page d'administration
  await page.goto("/admin/enseigne/list");

  // Vérifier que nous sommes sur la page de liste des enseignes
  await expect(page.locator("h3")).toContainText("Liste des programmes");

  // Cliquer sur le bouton pour créer une nouvelle enseigne
  await page.getByRole("link", { name: "Ajouter un programme" }).click();

  // Étape 1.1: Identification
  await expect(page.locator(".substep-title")).toContainText("Identification de l'enseigne");
  await fillIdentification(page);
  await page.getByRole("button", { name: "Suivant" }).click();

  // Étape 1.2: Options
  await expect(page.locator(".substep-title")).toContainText("Options");
  await fillOptions(page);
  await page.getByRole("button", { name: "Suivant" }).click();

  // Étape 1.3: Personnalisation
  await expect(page.locator(".substep-title")).toContainText("Personnalisation");
  await fillCustomization(page);
  await page.getByRole("button", { name: "Suivant" }).click();

  // Étape 1.4: Facturation
  await expect(page.locator(".substep-title")).toContainText("Facturation");
  await fillFacturation(page);
  await page.getByRole("button", { name: "Suivant" }).click();

  // Étape 2: Formulaire
  await expect(page.locator(".substep-title")).toContainText("Formulaire");
  await fillForm(page);
  await page.getByRole("button", { name: "Suivant" }).click();

  // Étape 3: Notifications
  await expect(page.locator(".substep-title")).toContainText("Notifications");
  await fillNotifications(page);
  await page.getByRole("button", { name: "Terminer" }).click();

  // Vérifier que nous sommes retournés à la liste des enseignes avec un message de succès
  await expect(page.locator("h3")).toContainText("Liste des programmes");
});

async function fillIdentification(page: Page) {
  await page.locator("#enseigne_identification_name").fill(enseigneData.identification.name);

  // Télécharger un logo (fichier PNG)
  const logoPath = path.join(process.cwd(), 'src/DataFixtures/files/sample-logo.png');
  await page.locator("#enseigne_identification_logoFile").setInputFiles(logoPath);

  await page.locator("#enseigne_identification_identifier").fill(enseigneData.identification.identifier);

  // Télécharger un contrat (fichier PDF)
  const contractPath = path.join(process.cwd(), 'src/DataFixtures/files/sample-contract.pdf');
  await page.locator("#enseigne_identification_contractFile").setInputFiles(contractPath);

  await page.locator("#enseigne_identification_pole").selectOption(enseigneData.identification.pole);
}

async function fillOptions(page: Page) {
  // Ajouter les options
  for (const option of enseigneData.options) {
    await page.getByRole("button", { name: "Ajouter une option" }).click();

    // Remplir les champs de la dernière option ajoutée
    const optionItems = page.locator("#options-list > div");
    const count = await optionItems.count();
    const lastOption = optionItems.nth(count - 1);

    await lastOption.locator("input[name$='[name]']").fill(option.name);
    await lastOption.locator("textarea[name$='[text]']").fill(option.description);
    await lastOption.locator("input[name$='[unitPrice]']").fill(option.unitPrice);

    if (option.isFree) {
      await lastOption.locator("input[name$='[isFree]']").check();
    }
  }
}

async function fillCustomization(page: Page) {
  await page.locator("#enseigne_customization_textPresentation").fill(enseigneData.customization.textPresentation);
  await page.locator("#enseigne_customization_textUnderLGO").fill(enseigneData.customization.textUnderLGO);
  await page.locator("#enseigne_customization_textBeforeSignature").fill(enseigneData.customization.textBeforeSignature);
  await page.locator("#enseigne_customization_textAfterSignature").fill(enseigneData.customization.textAfterSignature);
  await page.locator("#enseigne_customization_textContact").fill(enseigneData.customization.textContact);

  await page.locator("#enseigne_customization_color1").fill(enseigneData.customization.color1);
  await page.locator("#enseigne_customization_color2").fill(enseigneData.customization.color2);
  await page.locator("#enseigne_customization_color3").fill(enseigneData.customization.color3);

  await page.locator("#enseigne_customization_linkMobilityAccp").fill(enseigneData.customization.linkMobilityAccp);
}

async function fillFacturation(page: Page) {
  if (enseigneData.facturation.askForSepa) {
    await page.locator("#enseigne_facturation_askForSepa").check();
  } else {
    await page.locator("#enseigne_facturation_askForSepa").uncheck();
  }
}

async function fillForm(page: Page) {
  // Ajouter les sections et les champs
  for (const section of enseigneData.form.sections) {
    await page.getByRole("button", { name: "Ajouter une catégorie" }).click();

    // Remplir les champs de la dernière section ajoutée
    const sectionItems = page.locator("#sections-list > div");
    const count = await sectionItems.count();
    const lastSection = sectionItems.nth(count - 1);

    await lastSection.locator("input[name$='[title]']").fill(section.title);
    await lastSection.locator("textarea[name$='[description]']").fill(section.description);

    // Ajouter les champs à cette section en utilisant le drag and drop
    for (const field of section.fields) {
      // Trouver le champ dans la sidebar
      const sidebarField = page.locator(`.fields-sidebar .field-item[data-field-config*="${field.type}"]`).first();

      // Trouver la liste des champs de la section
      const targetFieldsList = lastSection.locator(".fields-list").first();

      // Effectuer le drag and drop
      const sidebarFieldBoundingBox = await sidebarField.boundingBox();
      const targetFieldsListBoundingBox = await targetFieldsList.boundingBox();

      if (sidebarFieldBoundingBox && targetFieldsListBoundingBox) {
        // Position de départ (centre du champ dans la sidebar)
        const startX = sidebarFieldBoundingBox.x + sidebarFieldBoundingBox.width / 2;
        const startY = sidebarFieldBoundingBox.y + sidebarFieldBoundingBox.height / 2;

        // Position d'arrivée (centre de la liste des champs de la section)
        const endX = targetFieldsListBoundingBox.x + targetFieldsListBoundingBox.width / 2;
        const endY = targetFieldsListBoundingBox.y + targetFieldsListBoundingBox.height / 2;

        // Effectuer le drag and drop
        await page.mouse.move(startX, startY);
        await page.mouse.down();
        await page.mouse.move(endX, endY, { steps: 10 }); // Déplacement progressif pour simuler un vrai drag
        await page.mouse.up();

        // Attendre que le champ soit ajouté au DOM
        await page.waitForTimeout(500);

        // Trouver le dernier champ ajouté
        const fieldItems = lastSection.locator(".fields-list > div");
        const fieldCount = await fieldItems.count();
        const lastField = fieldItems.nth(fieldCount - 1);

        // Remplir le label si nécessaire (certains champs formatés peuvent déjà avoir un label)
        await lastField.locator("input[name$='[label]']").fill(field.label);

        // Cocher si le champ est requis (sauf si c'est un champ formaté qui est déjà requis)
        const requiredCheckbox = lastField.locator("input[name$='[required]']");
        if (await requiredCheckbox.isEnabled()) {
          if (field.required) {
            await requiredCheckbox.check();
          } else {
            await requiredCheckbox.uncheck();
          }
        }
      }
    }
  }
}

async function fillNotifications(page: Page) {
  await page.locator("#enseigne_notifications_contractFilename").fill(enseigneData.notifications.contractFilename);
  await page.locator("#enseigne_notifications_emailSenderEmail").fill(enseigneData.notifications.emailSenderEmail);
  await page.locator("#enseigne_notifications_emailSenderName").fill(enseigneData.notifications.emailSenderName);

  // Notifications Aquitem
  if (enseigneData.notifications.emailAquitemEnabled) {
    await page.locator("#enseigne_notifications_emailAquitemEnabled").check();
  }
  if (enseigneData.notifications.emailAquitemAttachContract) {
    await page.locator("#enseigne_notifications_emailAquitemAttachContract").check();
  }
  await page.locator("#enseigne_notifications_emailAquitemRecipientEmail").fill(enseigneData.notifications.emailAquitemRecipientEmail);
  await page.locator("#enseigne_notifications_emailAquitemSubject").fill(enseigneData.notifications.emailAquitemSubject);
  await page.locator("#enseigne_notifications_emailAquitemContent").fill(enseigneData.notifications.emailAquitemContent);

  // Notifications Enseigne
  if (enseigneData.notifications.emailEnseigneEnabled) {
    await page.locator("#enseigne_notifications_emailEnseigneEnabled").check();
  }
  if (enseigneData.notifications.emailEnseigneAttachContract) {
    await page.locator("#enseigne_notifications_emailEnseigneAttachContract").check();
  }
  await page.locator("#enseigne_notifications_emailEnseigneRecipientEmail").fill(enseigneData.notifications.emailEnseigneRecipientEmail);
  await page.locator("#enseigne_notifications_emailEnseigneSubject").fill(enseigneData.notifications.emailEnseigneSubject);
  await page.locator("#enseigne_notifications_emailEnseigneContent").fill(enseigneData.notifications.emailEnseigneContent);

  // Notifications Adhérent
  if (enseigneData.notifications.emailSubscriberEnabled) {
    await page.locator("#enseigne_notifications_emailSubscriberEnabled").check();
  }
  if (enseigneData.notifications.emailSubscriberAttachContract) {
    await page.locator("#enseigne_notifications_emailSubscriberAttachContract").check();
  }
  await page.locator("#enseigne_notifications_emailSubscriberSubject").fill(enseigneData.notifications.emailSubscriberSubject);
  await page.locator("#enseigne_notifications_emailSubscriberContent").fill(enseigneData.notifications.emailSubscriberContent);
}
