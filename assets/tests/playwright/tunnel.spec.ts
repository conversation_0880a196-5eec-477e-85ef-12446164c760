import { test, expect, Page } from "@playwright/test";

let adhesion: any = {};
let cookieName = "test_env";
let cookieValue = process.env.APP_SECRET ?? "";
let cookieHeaders = {
  headers: {Cookie : `${cookieName}=${cookieValue}`}
};

test.beforeAll(async ({ request, baseURL }) => {
  const response = await request.get(`${baseURL.replace(/\/$/, "")}/test/start`, cookieHeaders);
  adhesion = await response.json();
});

test.afterAll(async ({ request, baseURL }) => {
  await request.get(`${baseURL.replace(/\/$/, "")}/test/clean`, cookieHeaders);
});

test.beforeEach(async ({ page, baseURL }) => {
  await page.context().clearCookies();
  await page.goto("/");
  // Ajout du cookie permettant de passer au travers des captchas + refus des cookies tarteaucitron
  await page.context().addCookies([
    {
      name: cookieName,
      value: cookieValue,
      url: baseURL,
    },
  ]);

});

test("Tunnel d'adhésion", async ({ page }) => {
  const identifier = "PLAYWRIGHT";

  await page.goto(`/c/${identifier}`);

  // Page d'accueil
  await page.getByRole("link", { name: "Commencer" }).click();
  await expect(page).toHaveURL(new RegExp(`\/c\/${identifier}\/1`));

  // Etape 1
  await page.getByRole("link", { name: "Poursuivre" }).click();
  await expect(page).toHaveURL(new RegExp(`\/c\/${identifier}\/2`));

  // Etape 2
  await page.getByRole("button", { name: "Poursuivre" }).click();
  await expect(page).toHaveURL(new RegExp(`\/c\/${identifier}\/3`));

  // Etape 3
  await fillFormInfos(page);
  await page.getByRole("button", { name: "Poursuivre" }).click();

  await expect(page).toHaveURL(new RegExp(`\/c\/${identifier}\/4`));

  // Etape 4
  await fillSepa(page);
  await page.getByRole("button", { name: "Poursuivre" }).click();
  let requiredPin = "";
  const responseListener = async (response) => {
    const responsePin = (await response.headerValue("x-adhesion-pin")) as string;
    if (responsePin) {
      requiredPin = responsePin;
      page.off("response", responseListener);
    }
  };
  page.on("response", responseListener);
  await expect(page).toHaveURL(new RegExp(`\/c\/${identifier}\/5`));

  // Etape 5
  await page.locator("#adhesion_signature_cgvAccepted").check();
  await page.locator("#adhesion_signature_sepaAccepted").check();
  await page.locator("#adhesion_signature_debitAccepted").check();
  await page.locator("#adhesion_signature_pin").fill(requiredPin);
  await drawSignature(page);
  await page.getByRole("button", { name: "Signer mon contrat" }).click();
  await expect(page).toHaveURL(new RegExp(`\/c\/${identifier}\/6`));

  // Etape 6
  const popupPromise = page.waitForEvent("popup");
  await page.getByRole("link", { name: " Télécharger mon contrat signé" }).click();
  await popupPromise;
});

async function fillFormInfos(page: Page) {
  const date = new Date(adhesion.infos.dynamicFields["informations-generales-date-souhaitee-d-entree-dans-le-programme"].date);
  // Informations générales
  await fillInfos(page, "informations-generales-votre-numero-finess", adhesion.infos.dynamicFields["informations-generales-votre-numero-finess"], true);
  await fillInfos(page, "informations-generales-votre-code-id-pharm-upp", adhesion.infos.dynamicFields["informations-generales-votre-code-id-pharm-upp"], true);
  await fillInfos(page, "informations-generales-siret", adhesion.infos.dynamicFields["informations-generales-siret"]);
  await fillInfos(page, "informations-generales-raison-sociale", adhesion.infos.dynamicFields["informations-generales-raison-sociale"]);
  await fillInfos(page, "informations-generales-date-souhaitee-d-entree-dans-le-programme", date.toLocaleString().split(' ')[0], true);
  await selectOption(page, "informations-generales-votre-statut", adhesion.infos.dynamicFields["informations-generales-votre-statut"]);

  // Coordonnées
  await fillInfos(page, "coordonnees-votre-libelle-de-pharmacie-ex-pharmacie-de-la-poste", adhesion.infos.dynamicFields["coordonnees-votre-libelle-de-pharmacie-ex-pharmacie-de-la-poste"]);
  await fillInfos(page, "coordonnees-adresse", adhesion.infos.dynamicFields["coordonnees-adresse"]);
  await fillInfos(page, "coordonnees-complement-d-adresse", adhesion.infos.dynamicFields["coordonnees-complement-d-adresse"]);
  await fillInfos(page, "coordonnees-code-postal", adhesion.infos.dynamicFields["coordonnees-code-postal"]);
  await fillInfos(page, "coordonnees-ville", adhesion.infos.dynamicFields["coordonnees-ville"]);
  await fillInfos(page, "coordonnees-tel-fixe", adhesion.infos.dynamicFields["coordonnees-tel-fixe"]);

  // Responsable de la pharmacie
  await setRadio(page, "responsable-de-la-pharmacie-civilite", adhesion.infos.dynamicFields["responsable-de-la-pharmacie-civilite"]);
  await fillInfos(page, "responsable-de-la-pharmacie-prenom", adhesion.infos.dynamicFields["responsable-de-la-pharmacie-prenom"]);
  await fillInfos(page, "responsable-de-la-pharmacie-nom", adhesion.infos.dynamicFields["responsable-de-la-pharmacie-nom"]);
  await fillInfos(page, "responsable-de-la-pharmacie-tel-portable", adhesion.infos.dynamicFields["responsable-de-la-pharmacie-tel-portable"], true);
  await fillInfos(page, "responsable-de-la-pharmacie-adresse-e-mail", adhesion.infos.dynamicFields["responsable-de-la-pharmacie-adresse-e-mail"]);

  // Responsable de la comptabilité
  await setRadio(page, "responsable-de-la-comptabilite-civilite", adhesion.infos.dynamicFields["responsable-de-la-comptabilite-civilite"]);
  await fillInfos(page, "responsable-de-la-comptabilite-prenom", adhesion.infos.dynamicFields["responsable-de-la-comptabilite-prenom"]);
  await fillInfos(page, "responsable-de-la-comptabilite-nom", adhesion.infos.dynamicFields["responsable-de-la-comptabilite-nom"]);
  await fillInfos(page, "responsable-de-la-comptabilite-tel", adhesion.infos.dynamicFields["responsable-de-la-comptabilite-tel"], true);
  await fillInfos(page, "responsable-de-la-comptabilite-adresse-e-mail-de-reception-des-factures", adhesion.infos.dynamicFields["esponsable-de-la-comptabilite-adresse-e-mail-de-reception-des-factures"], true);

  // Prestataire informatique
  await selectOption(page, "votre-prestataire-informatique-votre-prestataire", adhesion.infos.dynamicFields["votre-prestataire-informatique-votre-prestataire"]);
  if (adhesion.infos.dynamicFields["votre-prestataire-informatique-autre"]) {
    await fillInfos(page, "votre-prestataire-informatique-autre", adhesion.infos.dynamicFields["votre-prestataire-informatique-autre"]);
  }
}

async function fillInfos(page: Page, id: string, value: string, type: boolean = false) {
  await page.locator(`#form_${id}`)[type ? "type" : "fill"](value);
}

async function selectOption(page: Page, id: string, value: string) {
  await page.locator(`#form_${id}`).selectOption(value);
}

async function setRadio(page: Page, id: string, value: string) {
  await page.locator(`[name="form[${id}]"][value="${value}"]`).check();
}

async function fillSepa(page: Page) {
  await page.locator("#adhesion_sepa_iban").type(adhesion.sepa.iban);
  await page.locator("#adhesion_sepa_bic").fill(adhesion.sepa.bic);
  await page.locator("#adhesion_sepa_signaturePhone").fill(adhesion.sepa.signaturePhone);
  await page.locator("#adhesion_sepa_sepaName").fill(adhesion.sepa.sepaName);
  await page.locator("#adhesion_sepa_address").fill(adhesion.sepa.address);
  if (adhesion.sepa.address2) {
    await page.locator("#adhesion_sepa_address2").fill(adhesion.sepa.address2);
  }
  await page.locator("#adhesion_sepa_zipCode").fill(adhesion.sepa.zipCode);
  await page.locator("#adhesion_sepa_city").fill(adhesion.sepa.city);
  // await page.locator("#adhesion_sepa_country").selectOption(adhesion.sepa.country);
}

async function drawSignature(page: Page) {
  const canvas = page.locator("#signature-pad").first();
  const canvasBoundingBox = await canvas.boundingBox();
  if (canvasBoundingBox) {
    let size = canvasBoundingBox.height / 2;
    let moveOpts = { steps: 10 };
    const x = canvasBoundingBox.x + canvasBoundingBox.width / 2 - size;
    const y = canvasBoundingBox.y - (size * 3) / 4;
    await page.mouse.move(x, y, moveOpts);
    await page.waitForTimeout(25);
    await page.mouse.down();
    await page.waitForTimeout(25);
    await page.mouse.move(x, y + size, moveOpts);
    await page.waitForTimeout(25);
    await page.mouse.move(x + size, y + size, moveOpts);
    await page.waitForTimeout(25);
    await page.mouse.move(x + size, y, moveOpts);
    await page.waitForTimeout(25);
    await page.mouse.move(x, y, moveOpts);
    await page.waitForTimeout(25);
    await page.mouse.up();
  }
}
