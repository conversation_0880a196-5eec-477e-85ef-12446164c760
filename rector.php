<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;

return RectorConfig::configure()
    ->withPaths([
        __DIR__.'/config',
        __DIR__.'/public',
        __DIR__.'/src',
        __DIR__.'/tests',
    ])
    ->withSets([
        <PERSON>\Set\ValueObject\LevelSetList::UP_TO_PHP_84,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_60,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_61,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_62,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_63,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_64,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_70,
        <PERSON>\Symfony\Set\SymfonySetList::SYMFONY_71,
        <PERSON>\Symfony\Set\SymfonySetList::SYMF<PERSON>Y_72,
        <PERSON>\Symfony\Set\SymfonySetList::SY<PERSON><PERSON><PERSON>_CODE_QUALITY,
        Rector\Symfony\Set\SymfonySetList::SYMFONY_CONSTRUCTOR_INJECTION,
        Rector\Doctrine\Set\DoctrineSetList::ANNOTATIONS_TO_ATTRIBUTES,
        Rector\Doctrine\Set\DoctrineSetList::DOCTRINE_ORM_300,
        Rector\Doctrine\Set\DoctrineSetList::DOCTRINE_DBAL_40,
        Rector\Doctrine\Set\DoctrineSetList::DOCTRINE_CODE_QUALITY,
        Rector\Doctrine\Set\DoctrineSetList::DOCTRINE_BUNDLE_210,
        Rector\PHPUnit\Set\PHPUnitSetList::PHPUNIT_110,
    ])
    ->withSkip([
        Rector\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector::class,
        Rector\Php74\Rector\Closure\ClosureToArrowFunctionRector::class,
        Rector\Symfony\CodeQuality\Rector\MethodCall\LiteralGetToRequestClassConstantRector::class,
        Rector\Symfony\Symfony34\Rector\Closure\ContainerGetNameToTypeInTestsRector::class, // provoqie des erreurs
    ])
    ->withSymfonyContainerXml(__DIR__.'/var/cache/dev/App_KernelDevDebugContainer.xml')
;
