<?php

namespace App\Tests\Integration;

use App\Factory\EnseigneFactory;
use App\Factory\FormFieldFactory;
use App\Factory\FormSectionFactory;
use App\Service\DynamicFormBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactoryInterface;
use Zenstruck\Foundry\Test\Factories;

class DynamicFormBuilderTest extends KernelTestCase
{
    use Factories;

    private DynamicFormBuilder $formBuilder;
    private FormFactoryInterface $factory;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->formBuilder = static::getContainer()->get(DynamicFormBuilder::class);
        $this->factory = static::getContainer()->get(FormFactoryInterface::class);
    }

    public function testDynamicFormBuilding(): void
    {
        // Création des fixtures avec Foundry
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Test Enseigne',
            'identifier' => 'TEST123',
        ]);

        $section = FormSectionFactory::createOne([
            'enseigne' => $enseigne,
            'title' => 'Informations générales',
            'position' => 1,
        ]);

        // Création de différents types de champs
        [$emailField, $nameField] = FormFieldFactory::createSequence([
            [
                'section' => $section,
                'label' => 'Adresse email',
                'type' => 'email',
                'required' => true,
                'help' => 'Entrez votre email professionnel',
                'position' => 1,
            ],
            [
                'section' => $section,
                'label' => 'Nom complet',
                'type' => 'text',
                'required' => true,
                'help' => 'Entrez votre nom complet',
                'position' => 2,
            ],
        ]);

        // Création du formulaire
        $builder = $this->factory->createBuilder();
        $this->formBuilder->buildForm($builder, $enseigne);

        // Récupération du formulaire construit
        $form = $builder->getForm();

        // Vérifications
        $this->assertTrue($form->has($emailField->getName()));
        $this->assertTrue($form->has($nameField->getName()));

        // Vérification du champ email
        $emailConfig = $form->get($emailField->getName())->getConfig();
        $this->assertEquals(EmailType::class, $emailConfig->getType()->getInnerType()::class);
        $this->assertTrue($emailConfig->getRequired());
        $this->assertEquals('Adresse email', $emailConfig->getOption('label'));
        $this->assertEquals('Entrez votre email professionnel', $emailConfig->getOption('help'));

        // Vérification du champ name
        $nameConfig = $form->get($nameField->getName())->getConfig();
        $this->assertEquals(TextType::class, $nameConfig->getType()->getInnerType()::class);
        $this->assertTrue($nameConfig->getRequired());
        $this->assertEquals('Nom complet', $nameConfig->getOption('label'));
        $this->assertEquals('Entrez votre nom complet', $nameConfig->getOption('help'));
    }

    public function testFormSubmissionValid(): void
    {
        // Création des fixtures
        $enseigne = EnseigneFactory::createOne();
        $section = FormSectionFactory::createOne(['enseigne' => $enseigne]);

        $emailField = FormFieldFactory::createOne([
            'section' => $section,
            'label' => 'Email',
            'type' => 'email',
            'required' => true,
        ]);

        // Création et soumission du formulaire
        $builder = $this->factory->createBuilder();
        $this->formBuilder->buildForm($builder, $enseigne);
        $form = $builder->getForm();

        // Test avec données valides
        $form->submit([
            $emailField->getName() => '<EMAIL>',
        ]);
        $this->assertTrue($form->isValid());
    }
}
