<?php

use Symfony\Component\Dotenv\Dotenv;

require dirname(__DIR__) . '/vendor/autoload.php';

if (file_exists(dirname(__DIR__) . '/config/bootstrap.php')) {
    require dirname(__DIR__) . '/config/bootstrap.php';
} elseif (method_exists(Dotenv::class, 'bootEnv')) {
    (new Dotenv())->bootEnv(dirname(__DIR__) . '/.env');
}

// Attention cette configuration est dupliquée dans le fichier zenstruck_foundry.yaml (pour éxécution avec le kernel)
Zenstruck\Foundry\Test\UnitTestConfig::configure(
    faker: Faker\Factory::create('fr_FR')
);
