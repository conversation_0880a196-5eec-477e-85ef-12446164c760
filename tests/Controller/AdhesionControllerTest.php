<?php

namespace App\Tests\Controller;

use App\Entity\Enseigne;
use App\Factory\EnseigneFactory;
use App\Model\AdhesionInfosFormModel;
use App\Model\AdhesionModel;
use App\Model\AdhesionSignatureFormModel;
use App\Service\DataMocker;
use App\Service\FormSessionHandler;
use App\Story\DefaultStory;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Test\MailerAssertionsTrait;
use Symfony\Bundle\FrameworkBundle\Test\NotificationAssertionsTrait;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\Storage\MockFileSessionStorage;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class AdhesionControllerTest extends WebTestCase
{
    use ResetDatabase;
    use Factories;
    use NotificationAssertionsTrait;
    use MailerAssertionsTrait;

    private function getTestEnseigne(): Enseigne
    {
        // Utiliser l'enseigne de démonstration créée par DefaultStory
        DefaultStory::load();

        // Récupérer l'enseigne de démonstration (Cartes Privilège PHARM-UPP)
        return EnseigneFactory::find(['identifier' => 'X456B73']);
    }

    private function getFormSessionHandler(): FormSessionHandler
    {
        return static::getContainer()->get(FormSessionHandler::class);
    }

    public function getMockedSession(): Session
    {
        $container = static::getContainer();

        $requestStack = $this->getMockBuilder(\Symfony\Component\HttpFoundation\RequestStack::class)
            ->disableOriginalConstructor()
            ->getMock();

        $session = new Session(new MockFileSessionStorage());
        $currentRequest = new Request(attributes: [
            'identifier' => 'X456B73',
        ]);

        $requestStack->method('getSession')->willReturn($session);
        $requestStack->method('getCurrentRequest')->willReturn($currentRequest);

        $formSessionHandler = new FormSessionHandler(
            $requestStack,
            $container->get(\Symfony\Component\Routing\RouterInterface::class),
            $container->get(\App\Repository\EnseigneRepository::class),
        );
        $container->set(FormSessionHandler::class, $formSessionHandler);

        return $session;
    }

    private function getAdhesionAtStep(int $step): AdhesionModel
    {
        $adhesion = $this->getFormSessionHandler()->createAdhesion();
        $adhesion->lastStep = $step;

        return $adhesion;
    }

    public function mockSessionState(Session $session, Enseigne $enseigne, AdhesionModel $adhesion): void
    {
        $session->set($this->getFormSessionHandler()->getSessionKey($enseigne), serialize($adhesion));
    }

    public function testAdhesionSummaryPage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();

        // Accéder à la page de résumé de l'adhésion
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier());

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.fw-bold', 'Rejoindre le programme ' . $enseigne->getName());

        // Vérifier que le texte de présentation est présent
        $this->assertSelectorTextContains('[data-test-id="presentation"]', preg_replace('/\s*\n+\s*/', ' ', $enseigne->getTextPresentation()));

        // Vérifier que le bouton pour commencer est présent
        $this->assertSelectorExists('a.btn-secondary');
        $this->assertSelectorTextContains('a.btn-secondary', 'Commencer');

        // Vérifier que le lien pointe vers la première étape
        $this->assertSelectorExists('a.btn-secondary[href*="/c/' . $enseigne->getIdentifier() . '/1"]');

        $newAdhesion = $this->getFormSessionHandler()->restoreAdhesion();

        $this->assertSame(0, $newAdhesion->lastStep);
    }

    public function testAdhesionStep1DiscoverPage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();
        $adhesion = $this->getAdhesionAtStep(0);

        $this->mockSessionState($session, $enseigne, $adhesion);

        // Accéder à la première étape (découverte)
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier() . '/1');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Vérifier que le PDF viewer est présent
        $this->assertSelectorExists('div[data-controller="pdf-viewer"]');

        // Vérifier que les boutons de navigation sont présents
        $this->assertSelectorExists('a.btn-primary');
        $this->assertSelectorTextContains('a.btn-primary', 'Retour');

        $this->assertSelectorExists('a.btn-secondary');
        $this->assertSelectorTextContains('a.btn-secondary', 'Poursuivre');

        // Vérifier que les liens pointent vers les bonnes pages
        $this->assertSelectorExists('a.btn-primary[href*="/c/' . $enseigne->getIdentifier() . '"]');
        $this->assertSelectorExists('a.btn-secondary[href*="/c/' . $enseigne->getIdentifier() . '/2"]');

        $newAdhesion = $this->getFormSessionHandler()->restoreAdhesion();

        $this->assertSame(1, $newAdhesion->lastStep);
    }

    public function testAdhesionStep2OptionsPage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();
        $adhesion = $this->getAdhesionAtStep(1);

        $this->mockSessionState($session, $enseigne, $adhesion);

        // Accéder à la deuxième étape (options)
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier() . '/2');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Vérifier que les options sont présentes
        $this->assertGreaterThanOrEqual(1, $crawler->filter('.row > .col-9 > .fw-bold')->count(), "Aucune option n'est présente sur la page");

        // Vérifier que les champs de quantité sont présents
        $this->assertGreaterThanOrEqual(1, $crawler->filter('input[type="number"]')->count(), "Aucun champ de quantité n'est présent sur la page");

        // Vérifier que les boutons de navigation sont présents
        $this->assertSelectorExists('a.btn-primary');
        $this->assertSelectorTextContains('a.btn-primary', 'Retour');

        $this->assertSelectorExists('input[type="submit"][value="Poursuivre"]');

        // Soumettre le formulaire avec des quantités
        $form = $crawler->selectButton('Poursuivre')->form();

        // Remplir tous les champs de quantité avec la valeur 1
        $formFields = $crawler->filter('input[type="number"]')->extract(['name']);
        foreach ($formFields as $fieldName) {
            $form[$fieldName] = 1;
        }

        // On réapplique la session car Symfony va reset le kernel à la prochaine requête
        $client->getKernel()->shutdown();
        $client->getKernel()->boot();
        $client->disableReboot();
        $session = $this->getMockedSession();
        $this->mockSessionState($session, $enseigne, $adhesion);

        $client->submit($form);

        // Vérifier la redirection vers l'étape suivante
        $this->assertResponseRedirects('/c/' . $enseigne->getIdentifier() . '/3');
        $client->followRedirect();

        // Vérifier que nous sommes bien sur la page des informations
        $this->assertSelectorExists('.fw-bold');

        $newAdhesion = $this->getFormSessionHandler()->restoreAdhesion();

        $this->assertSame(2, $newAdhesion->lastStep);
    }

    public function testAdhesionStep3InfosPage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();
        $adhesion = $this->getAdhesionAtStep(2);

        $this->mockSessionState($session, $enseigne, $adhesion);

        // Accéder à la troisième étape (informations)
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier() . '/3');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Vérifier qu'au moins une section est présente
        $this->assertSelectorExists('.fw-bold');

        // Vérifier que des champs sont présents
        $this->assertGreaterThanOrEqual(1, $crawler->filter('input, select, textarea')->count(), "Aucun champ n'est présent sur la page");

        // Vérifier que le texte sous LGO est présent
        $this->assertSelectorTextContains('.card-body', $enseigne->getTextUnderLGO());

        // Vérifier que les boutons de navigation sont présents
        $this->assertSelectorExists('a.btn-primary');
        $this->assertSelectorTextContains('a.btn-primary', 'Retour');

        $this->assertSelectorExists('input[type="submit"][value="Poursuivre"]');

        // Soumettre le formulaire avec des données valides
        $form = $crawler->selectButton('Poursuivre')->form();

        $form['form[informations-generales-votre-numero-finess]'] = '122222222';
        $form['form[informations-generales-votre-code-id-pharm-upp]'] = 'FN0012AB';
        $form['form[informations-generales-siret]'] = '12345678910123';
        $form['form[informations-generales-raison-sociale]'] = 'Thomas Choquet';
        $form['form[informations-generales-date-souhaitee-d-entree-dans-le-programme]'] = (new \DateTime())->format('Y-m-d');
        $form['form[informations-generales-votre-statut]'] = 'Essentiel';
        $form['form[coordonnees-votre-libelle-de-pharmacie-ex-pharmacie-de-la-poste]'] = 'Pharmacie de la poste';
        $form['form[coordonnees-adresse-e-mail-de-la-pharmacie]'] = '<EMAIL>';
        $form['form[coordonnees-site-internet-de-la-pharmacie]'] = 'https://pharmacie.com';
        $form['form[coordonnees-adresse]'] = '375 avenue de Tivoli';
        $form['form[coordonnees-complement-d-adresse]'] = '';
        $form['form[coordonnees-code-postal]'] = '33110';
        $form['form[coordonnees-ville]'] = 'Le Bouscat';
        $form['form[coordonnees-tel-fixe]'] = '+33612345678';
        $form['form[coordonnees-fax]'] = '+33612345678';
        $form['form[responsable-de-la-pharmacie-prenom]'] = 'Thomas';
        $form['form[responsable-de-la-pharmacie-nom]'] = 'Choquet';
        $form['form[responsable-de-la-pharmacie-tel-portable]'] = '+33612345678';
        $form['form[responsable-de-la-pharmacie-adresse-e-mail]'] = '<EMAIL>';
        $form['form[responsable-de-la-comptabilite-prenom]'] = 'Thomas';
        $form['form[responsable-de-la-comptabilite-nom]'] = 'Choquet';
        $form['form[responsable-de-la-comptabilite-tel]'] = '+33612345678';
        $form['form[responsable-de-la-comptabilite-adresse-e-mail-de-reception-des-factures]'] = '<EMAIL>';
        $form['form[votre-prestataire-informatique-votre-prestataire]'] = 'LGPI/ Pharmagest';
        $form['form[votre-prestataire-informatique-autre]'] = '';

        // On réapplique la session car Symfony va reset le kernel à la prochaine requête
        $client->getKernel()->shutdown();
        $client->getKernel()->boot();
        $client->disableReboot();
        $session = $this->getMockedSession();
        $this->mockSessionState($session, $enseigne, $adhesion);

        $client->submit($form);

        // Vérifier la redirection vers l'étape suivante
        $this->assertResponseRedirects('/c/' . $enseigne->getIdentifier() . '/4');

        $newAdhesion = $this->getFormSessionHandler()->restoreAdhesion();

        $this->assertSame(3, $newAdhesion->lastStep);
        $this->assertSame('122222222', $newAdhesion->infos->getDynamicValue('informations-generales-votre-numero-finess'));
    }

    public function testAdhesionStep4SepaPage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();
        $adhesion = $this->getAdhesionAtStep(3);

        // Initialiser les options et les infos (nécessaires pour l'étape 4)
        $adhesion->options = new ArrayCollection();
        $adhesion->infos = new AdhesionInfosFormModel();
        $adhesion->infos->dynamicFields = [
            'managerEmail' => '<EMAIL>',
            'finess' => '332123456',
        ];

        $this->mockSessionState($session, $enseigne, $adhesion);

        // Accéder à la quatrième étape (SEPA)
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier() . '/4');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Vérifier que les champs SEPA sont présents
        $this->assertSelectorExists('input[id$="iban"]');
        $this->assertSelectorExists('input[id$="bic"]');
        $this->assertSelectorExists('input[id$="signaturePhone"]');

        // Vérifier que les boutons de navigation sont présents
        $this->assertSelectorExists('a.btn-primary');
        $this->assertSelectorTextContains('a.btn-primary', 'Retour');

        $this->assertSelectorExists('input[type="submit"][value="Poursuivre"]');

        $form = $crawler->selectButton('Poursuivre')->form();

        $form['adhesion_sepa[iban]'] = '***************************';
        $form['adhesion_sepa[bic]'] = 'AGRIFRPP';
        $form['adhesion_sepa[signaturePhone]'] = '+33612345678';
        $form['adhesion_sepa[sepaName]'] = 'Thomas Choquet';
        $form['adhesion_sepa[address]'] = '375 avenue de Tivoli';
        $form['adhesion_sepa[address2]'] = 'Batiment Aquitem';
        $form['adhesion_sepa[zipCode]'] = '33110';
        $form['adhesion_sepa[city]'] = 'Saint-Remy-en-Bouzemont';

        // On réapplique la session car Symfony va reset le kernel à la prochaine requête
        $client->getKernel()->shutdown();
        $client->getKernel()->boot();
        $client->disableReboot();
        $session = $this->getMockedSession();
        $this->mockSessionState($session, $enseigne, $adhesion);

        $client->submit($form);

        $this->assertResponseRedirects('/c/' . $enseigne->getIdentifier() . '/5');

        $this->assertNotificationCount(1);

        // Vérifcation du SMS envoyé
        $message = $this->getNotifierMessage();
        $this->assertEquals('+33612345678', $message->getRecipientId());
        $this->assertStringContainsString('Pour valider électroniquement votre adhésion au programme Cartes Privilège PHARM-UPP, renseignez le code : ', $message->getSubject());

        $newAdhesion = $this->getFormSessionHandler()->restoreAdhesion();

        $this->assertSame(4, $newAdhesion->lastStep);
        $this->assertSame('***************************', $newAdhesion->sepa->iban);
    }

    public function testAdhesionStep5SignaturePage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();
        $adhesion = $this->getAdhesionAtStep(4);

        $adhesion->sepa = DataMocker::mockSepa();
        $adhesion->infos = DataMocker::mockInfos();
        $adhesion->options = DataMocker::mockOptions($enseigne);

        // Initialiser la signature (nécessaire pour l'étape 5)
        $adhesion->signature = new AdhesionSignatureFormModel();
        $adhesion->signature->requiredPin = '1234'; // Code PIN prédéfini pour le test
        $adhesion->signature->pinSentDate = new \DateTime();

        $this->mockSessionState($session, $enseigne, $adhesion);

        // Accéder à la cinquième étape (signature)
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier() . '/5');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Vérifier que le PDF viewer est présent
        $this->assertSelectorExists('div[data-controller="pdf-viewer"]');

        // Vérifier que les champs de signature sont présents
        $this->assertSelectorExists('input[id$="cgvAccepted"]');
        $this->assertSelectorExists('input[id$="signatureData"]');
        $this->assertSelectorExists('input[id$="pin"]');

        // Vérifier que les boutons de navigation sont présents
        $this->assertSelectorExists('a.btn-primary');
        $this->assertSelectorTextContains('a.btn-primary', 'Retour');

        // Soumettre le formulaire avec des données valides
        $form = $crawler->selectButton('Signer mon contrat')->form();
        $form['adhesion_signature[cgvAccepted]'] = 1;
        $form['adhesion_signature[signatureData]'] = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAACWCAYAAABkW7XSAAAAAXNSR0IArs4c6QAABGJJREFUeF7t1AEJAAAMAsHZv/RyPNwSyDncOQIECEQEFskpJgECBM5geQICBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAAYPlBwgQyAgYrExVghIgYLD8AAECGQGDlalKUAIEDJYfIEAgI2CwMlUJSoCAwfIDBAhkBAxWpipBCRAwWH6AAIGMgMHKVCUoAQIGyw8QIJARMFiZqgQlQMBg+QECBDICBitTlaAECBgsP0CAQEbAYGWqEpQAgQdWMQCX4yW9owAAAABJRU5ErkJggg==';
        $form['adhesion_signature[pin]'] = '1234';
        $form['adhesion_signature[sepaAccepted]'] = 1;
        $form['adhesion_signature[debitAccepted]'] = 1;

        // On réapplique la session car Symfony va reset le kernel à la prochaine requête
        $client->getKernel()->shutdown();
        $client->getKernel()->boot();
        $client->disableReboot();
        $session = $this->getMockedSession();
        $this->mockSessionState($session, $enseigne, $adhesion);

        $client->submit($form);

        // Vérifier la redirection vers l'étape suivante
        $this->assertResponseRedirects('/c/' . $enseigne->getIdentifier() . '/6');

        $newAdhesion = $this->getFormSessionHandler()->restoreAdhesion();

        $this->assertSame(5, $newAdhesion->lastStep);
        $this->assertTrue($newAdhesion->signature->signed);
        $this->assertNotNull($newAdhesion->signature->signatureDate);

        // Vérification envoi des emails
        $this->assertEmailCount(3);
        $emails = $this->getMailerMessages();
        $this->assertEmailHeaderSame($emails[0], 'To', '<EMAIL>');
        $this->assertEmailHeaderSame($emails[1], 'To', '<EMAIL>');
        $this->assertEmailHeaderSame($emails[2], 'To', '<EMAIL>');

        $this->assertEmailSubjectContains($emails[0], '[122222222] Adhésion Cartes Privilège PHARM-UPP : Pharmacie de la poste');
        $this->assertEmailSubjectContains($emails[1], '[122222222] Adhésion Cartes Privilège PHARM-UPP : Pharmacie de la poste');
        $this->assertEmailSubjectContains($emails[2], 'Programme Cartes Privilège');

        $this->assertEmailTextBodyContains($emails[0], 'Bonjour');
        $this->assertEmailTextBodyContains($emails[1], 'Bonjour');
        $this->assertEmailTextBodyContains($emails[2], 'Bonjour');

        $this->assertEmailAttachmentCount($emails[0], 1);
        $this->assertEmailAttachmentCount($emails[1], 1);
        $this->assertEmailAttachmentCount($emails[2], 1);
    }

    public function testAdhesionStep6EndPage(): void
    {
        $client = static::createClient();

        $session = $this->getMockedSession();

        // Récupérer l'enseigne de test
        $enseigne = $this->getTestEnseigne();
        $adhesion = $this->getAdhesionAtStep(5);

        $adhesion->sepa = DataMocker::mockSepa();
        $adhesion->infos = DataMocker::mockInfos();
        $adhesion->options = DataMocker::mockOptions($enseigne);
        $adhesion->signature = DataMocker::mockSignature();

        $this->mockSessionState($session, $enseigne, $adhesion);

        // Accéder à la sixième étape (fin)
        $crawler = $client->request('GET', '/c/' . $enseigne->getIdentifier() . '/6');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Vérifier que le message de confirmation est présent
        $this->assertSelectorTextContains('.fw-bold', 'Votre contrat d\'adhésion est signé');

        // Vérifier que le lien de téléchargement du contrat est présent
        $this->assertSelectorExists('a[href*="/pdf/contract/X456B73"]');
        $this->assertSelectorTextContains('a[href*="/pdf/contract/X456B73"]', 'Télécharger mon contrat signé');

        // Vérifier que le texte après signature est présent
        $this->assertSelectorTextContains('.card-body', preg_replace('/\s*\n+\s*/', ' ', $enseigne->getTextAfterSignature()));
    }
}
