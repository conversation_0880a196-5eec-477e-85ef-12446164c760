<?php

namespace App\Tests\Controller\Admin;

use App\Entity\Enseigne;
use App\Factory\EnseigneFactory;
use App\Factory\FormFieldFactory;
use App\Factory\FormSectionFactory;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class EnseigneControllerTest extends WebTestCase
{
    use ResetDatabase;
    use Factories;

    public function testList(): void
    {
        $client = static::createClient();

        // Arrange
        // Créer des enseignes de test avec Foundry
        EnseigneFactory::createSequence([
            [
                'name' => 'Enseigne de test 0',
                'identifier' => 'TEST0',
                'formCompleted' => true, // Marquer comme complété pour tester le lien app_adhesion_summary
            ],
            [
                'name' => 'Enseigne de test 1',
                'identifier' => 'TEST1',
                'formCompleted' => false, // Non complété pour tester l'absence de lien
            ],
            [
                'name' => 'Enseigne de test 2',
                'identifier' => 'TEST2',
                'formCompleted' => true,
            ],
        ]);

        // Act
        // Accéder à la route de liste
        $crawler = $client->request('GET', '/admin/enseigne/list');

        // Assert
        // Vérifier que la requête a réussi
        $this->assertResponseIsSuccessful();

        // Vérifier que la page contient le bon template
        $this->assertSelectorTextContains('h3', 'Liste des programmes');

        // Vérifier que les 3 enseignes sont présentes dans la réponse
        $this->assertCount(3, $crawler->filter('ul.list-group > li'));

        // Vérifier que les noms des enseignes sont bien affichés
        $this->assertSelectorTextContains('ul.list-group > li:nth-child(1)', 'Enseigne de test 0');
        $this->assertSelectorTextContains('ul.list-group > li:nth-child(2)', 'Enseigne de test 1');
        $this->assertSelectorTextContains('ul.list-group > li:nth-child(3)', 'Enseigne de test 2');

        // Vérifier la présence du lien app_adhesion_summary pour les enseignes complétées
        $this->assertSelectorExists('ul.list-group > li:nth-child(1) a[href*="/c/TEST0"]');
        $this->assertSelectorNotExists('ul.list-group > li:nth-child(2) a[href*="/c/TEST1"]'); // Pas de lien pour l'enseigne non complétée
        $this->assertSelectorExists('ul.list-group > li:nth-child(3) a[href*="/c/TEST2"]');

        // Vérifier la présence des liens modifier et supprimer pour chaque enseigne
        for ($i = 1; $i <= 3; ++$i) {
            $this->assertSelectorExists("ul.list-group > li:nth-child($i) a.btn-primary[href*='edit']", "Le lien modifier est absent pour l'enseigne $i");
            $this->assertSelectorTextContains("ul.list-group > li:nth-child($i) a.btn-primary[href*='edit']", 'Modifier');

            $this->assertSelectorExists("ul.list-group > li:nth-child($i) a.btn-danger[href*='delete']", "Le lien supprimer est absent pour l'enseigne $i");
            $this->assertSelectorTextContains("ul.list-group > li:nth-child($i) a.btn-danger[href*='delete']", 'Supprimer');
        }

        // Vérifier la présence du lien pour ajouter un programme
        $this->assertSelectorExists('a.btn-primary[href*="create"]');
        $this->assertSelectorTextContains('a.btn-primary[href*="create"]', 'Ajouter un programme');
    }

    public function testCreateEnseigne(): void
    {
        $client = static::createClient();

        // Act - Cliquer sur le bouton de création d'une enseigne
        $crawler = $client->request('GET', '/admin/enseigne/list');
        $client->clickLink('Ajouter un programme');

        // Assert - Vérifier la redirection vers le formulaire d'identification
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page d'identification
        $this->assertSelectorTextContains('.substep-title', 'Identification de l\'enseigne');

        // Préparer les fichiers pour le test
        $projectDir = static::getContainer()->getParameter('kernel.project_dir');

        // Soumettre le formulaire d'identification
        $form = $client->getCrawler()->selectButton('Suivant')->form();
        $form['enseigne_identification[name]'] = 'Nouvelle Enseigne Test';
        $form['enseigne_identification[identifier]'] = 'NEWTEST';
        $form['enseigne_identification[pole]'] = Enseigne::POLE_SANTE;

        // Ajouter les fichiers au formulaire
        $form['enseigne_identification[logoFile]'] = new UploadedFile(
            $projectDir . '/src/DataFixtures/files/sample-logo.png',
            'logo-test.png',
            'image/png',
            null,
            true
        );

        $form['enseigne_identification[contractFile]'] = new UploadedFile(
            $projectDir . '/src/DataFixtures/files/sample-contract.pdf',
            'contract-test.pdf',
            'application/pdf',
            null,
            true
        );

        $client->submit($form);

        // Vérifier la redirection vers l'étape suivante (options)
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page des options
        $this->assertSelectorTextContains('.substep-title', 'Options');

        // Vérifier que l'enseigne a bien été créée en base de données
        $enseigne = EnseigneFactory::find(['identifier' => 'NEWTEST']);

        $this->assertNotNull($enseigne, "L'enseigne n'a pas été créée en base de données");
        $this->assertEquals('Nouvelle Enseigne Test', $enseigne->getName());
        $this->assertEquals(Enseigne::POLE_SANTE, $enseigne->getPole());
        $this->assertNotNull($enseigne->getLogo(), "Le logo n'a pas été uploadé");
        $this->assertNotNull($enseigne->getContract(), "Le contrat n'a pas été uploadé");
    }

    public function testOptionsPage(): void
    {
        $client = static::createClient();

        // Créer une enseigne pour tester la page des options
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Enseigne Options Test',
            'identifier' => 'OPTTEST',
            'pole' => Enseigne::POLE_SANTE,
            'logo' => 'sample-logo.png',
            'contract' => 'sample-contract.pdf',
            'options' => [],
        ]);

        // Accéder directement à la page des options
        $client->request('GET', '/admin/enseigne/' . $enseigne->getId() . '/options');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.substep-title', 'Options');

        // Vérifier la présence du bouton pour ajouter une option (même si on ne peut pas le cliquer dans le test)
        $this->assertSelectorExists('button[data-action="form-collection#addItem"]');
        $this->assertSelectorTextContains('button[data-action="form-collection#addItem"]', 'Ajouter une option');

        $formData = [
            'enseigne_options' => [
                'options' => [
                    0 => [
                        'name' => 'Option de test',
                        'text' => 'Description de l\'option de test',
                        'hasQuantity' => '1',
                        'unitPrice' => '10.50',
                        'position' => '0',
                    ],
                    1 => [
                        'name' => 'Autre option de test',
                        'text' => 'Description de l\'autre option de test',
                        'isFree' => '1',
                        'position' => '1',
                    ],
                ],
            ],
        ];

        // Soumettre les données directement
        $client->request(
            'POST',
            '/admin/enseigne/' . $enseigne->getId() . '/options',
            $formData
        );

        // Vérifier la redirection vers l'étape suivante (customization)
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page de personnalisation
        $this->assertSelectorTextContains('.substep-title', 'Personnalisation');

        // Vérifier que l'option a bien été créée en base de données
        $enseigne->_refresh();
        $options = $enseigne->getOptions();

        $this->assertCount(2, $options, "Les options n'ont pas été ajoutées à l'enseigne");
        $option = $options->first();

        $this->assertEquals('Option de test', $option->getName());
        $this->assertEquals('Description de l\'option de test', $option->getText());
        $this->assertTrue($option->isHasQuantity());
        $this->assertFalse($option->isIsFree());
        $this->assertFalse($option->isNoPriceDisplayed());
        $this->assertEquals(10.50, $option->getUnitPrice());
        $this->assertEquals(0, $option->getPosition());

        $anotherOption = $options->last();
        $this->assertEquals('Autre option de test', $anotherOption->getName());
        $this->assertEquals('Description de l\'autre option de test', $anotherOption->getText());
        $this->assertFalse($anotherOption->isHasQuantity());
        $this->assertTrue($anotherOption->isIsFree());
        $this->assertFalse($anotherOption->isNoPriceDisplayed());
        $this->assertNull($anotherOption->getUnitPrice());
        $this->assertEquals(1, $anotherOption->getPosition());
    }

    public function testCustomizationPage(): void
    {
        $client = static::createClient();

        // Créer une enseigne pour tester la page de personnalisation
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Enseigne Customization Test',
            'identifier' => 'CUSTTEST',
            'pole' => Enseigne::POLE_SANTE,
        ]);

        // Accéder directement à la page de personnalisation
        $client->request('GET', '/admin/enseigne/' . $enseigne->getId() . '/customization');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.substep-title', 'Personnalisation');

        $form = $client->getCrawler()->selectButton('Suivant')->form();

        // Préparer les données du formulaire
        $formData = [
            'enseigne_customization' => [
                'textPresentation' => 'Texte de présentation de test',
                'textUnderLGO' => 'Texte sous LGO de test',
                'textBeforeSignature' => 'Texte avant signature de test',
                'textAfterSignature' => 'Texte après signature de test',
                'textContact' => 'Texte de contact de test',
                'color1' => '#FF0000',
                'color2' => '#00FF00',
                'color3' => '#0000FF',
                'linkMobilityAccp' => 'AGSI01_123456789',
            ],
        ];

        $client->submit($form, $formData);

        // Vérifier la redirection vers l'étape suivante (facturation)
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page de facturation
        $this->assertSelectorTextContains('.substep-title', 'Facturation');

        // Vérifier que les données ont bien été enregistrées
        $enseigne->_refresh();

        $this->assertEquals('Texte de présentation de test', $enseigne->getTextPresentation());
        $this->assertEquals('Texte sous LGO de test', $enseigne->getTextUnderLGO());
        $this->assertEquals('Texte avant signature de test', $enseigne->getTextBeforeSignature());
        $this->assertEquals('Texte après signature de test', $enseigne->getTextAfterSignature());
        $this->assertEquals('Texte de contact de test', $enseigne->getTextContact());
        $this->assertEquals('#FF0000', $enseigne->getColor1());
        $this->assertEquals('#00FF00', $enseigne->getColor2());
        $this->assertEquals('#0000FF', $enseigne->getColor3());
        $this->assertEquals('AGSI01_123456789', $enseigne->getLinkMobilityAccp());
    }

    public function testFacturationPage(): void
    {
        $client = static::createClient();

        // Créer une enseigne pour tester la page de facturation
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Enseigne Facturation Test',
            'identifier' => 'FACTTEST',
            'pole' => Enseigne::POLE_SANTE,
            'askForSepa' => false, // Par défaut, on ne demande pas le SEPA
        ]);

        // Accéder directement à la page de facturation
        $client->request('GET', '/admin/enseigne/' . $enseigne->getId() . '/facturation');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.substep-title', 'Facturation');

        $form = $client->getCrawler()->selectButton('Suivant')->form();

        // Préparer les données du formulaire
        $formData = [
            'enseigne_facturation' => [
                'askForSepa' => '1',
            ],
        ];

        $client->submit($form, $formData);

        // Vérifier la redirection vers l'étape suivante (formulaire)
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page du formulaire
        $this->assertSelectorTextContains('.substep-title', 'Formulaire');

        // Vérifier que les données ont bien été enregistrées
        $enseigne->_refresh();

        // Vérifier que l'option askForSepa a bien été activée
        $this->assertTrue($enseigne->getAskForSepa(), "L'option de demande de SEPA n'a pas été activée");
    }

    public function testFormPage(): void
    {
        $client = static::createClient();

        // Créer une enseigne pour tester la page du formulaire
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Enseigne Form Test',
            'identifier' => 'FORMTEST',
            'pole' => Enseigne::POLE_SANTE,
            'sections' => [], // Aucune section au départ
        ]);

        // Accéder directement à la page du formulaire
        $client->request('GET', '/admin/enseigne/' . $enseigne->getId() . '/form');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.substep-title', 'Formulaire');

        // Préparer les données du formulaire avec une section et des champs
        $formData = [
            'enseigne_formulaire_dynamique' => [
                'sections' => [
                    0 => [
                        'title' => 'Informations générales',
                        'description' => 'Veuillez remplir les informations générales',
                        'position' => '0',
                        'fields' => [
                            1 => [
                                'label' => 'Adresse e-mail du responsable',
                                'type' => 'managerEmail',
                                'required' => '1',
                                'help' => 'Vous recevrez par e-mail le contrat signé à la fin de la procédure d\'adhésion',
                                'position' => '0',
                                'options' => '',
                                'pdfDisplayConfig' => [
                                    'summary' => '1',
                                    'signature' => '1',
                                ],
                            ],
                            0 => [
                                'label' => 'FINESS',
                                'type' => 'finess',
                                'required' => '1',
                                'help' => 'N° commençant par le numéro de votre département et suivi du chiffre 2 (ex : 332123456)',
                                'position' => '1',
                                'options' => '',
                                'pdfDisplayConfig' => [
                                    'summary' => '1',
                                    'signature' => '1',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        // Soumettre les données directement
        $client->request(
            'POST',
            '/admin/enseigne/' . $enseigne->getId() . '/form',
            $formData
        );

        // Vérifier la redirection vers l'étape suivante (notifications)
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page des notifications
        $this->assertSelectorTextContains('.substep-title', 'Notifications');

        // Vérifier que les données ont bien été enregistrées
        $enseigne->_refresh();

        // Vérifier que la section a bien été créée
        $sections = $enseigne->getSections();
        $this->assertCount(1, $sections, "La section n'a pas été créée");

        $section = $sections->first();
        $this->assertEquals('Informations générales', $section->getTitle());
        $this->assertEquals('Veuillez remplir les informations générales', $section->getDescription());
        $this->assertEquals(0, $section->getPosition());

        // Vérifier que les champs ont bien été créés
        $fields = $section->getFields();
        $this->assertCount(2, $fields, "Les champs n'ont pas été créés");

        $managerEmailField = $fields->filter(fn ($field) => 'managerEmail' === $field->getType())->first();
        $this->assertNotNull($managerEmailField, "Le champ managerEmail n'a pas été créé");
        $this->assertEquals('Adresse e-mail du responsable', $managerEmailField->getLabel());
        $this->assertTrue($managerEmailField->isRequired());

        $finessField = $fields->filter(fn ($field) => 'finess' === $field->getType())->first();
        $this->assertNotNull($finessField, "Le champ finess n'a pas été créé");
        $this->assertEquals('FINESS', $finessField->getLabel());
        $this->assertTrue($finessField->isRequired());
    }

    public function testFormPageSubmitWithoutRequiredField(): void
    {
        $client = static::createClient();

        // Créer une enseigne pour tester la page du formulaire
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Enseigne Form Error Test',
            'identifier' => 'FORMERR',
            'pole' => Enseigne::POLE_SANTE,
            'sections' => [], // Aucune section au départ
        ]);

        // Accéder directement à la page du formulaire
        $client->request('GET', '/admin/enseigne/' . $enseigne->getId() . '/form');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();

        // Préparer les données du formulaire avec une section mais SANS le champ managerEmail obligatoire
        $formData = [
            'enseigne_formulaire_dynamique' => [
                'sections' => [
                    0 => [
                        'title' => 'Informations générales',
                        'description' => 'Veuillez remplir les informations générales',
                        'position' => '0',
                        'fields' => [
                            // Uniquement le champ FINESS, pas de managerEmail
                            0 => [
                                'label' => 'FINESS',
                                'type' => 'finess',
                                'required' => '1',
                                'help' => 'N° commençant par le numéro de votre département et suivi du chiffre 2 (ex : 332123456)',
                                'position' => '0',
                                'options' => '',
                                'pdfDisplayConfig' => [
                                    'summary' => '1',
                                    'signature' => '1',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        // Soumettre les données directement
        $client->request(
            'POST',
            '/admin/enseigne/' . $enseigne->getId() . '/form',
            $formData
        );

        // Vérifier que la réponse est une erreur 422 (Unprocessable Entity)
        $this->assertResponseStatusCodeSame(422);

        // Vérifier que le message d'erreur concernant le champ managerEmail est présent
        $this->assertSelectorTextContains('.alert-danger', 'La présence du champ Adresse e-mail responsable est obligatoire');

        // Vérifier que nous sommes toujours sur la page du formulaire
        $this->assertSelectorTextContains('.substep-title', 'Formulaire');

        // Vérifier qu'aucune section n'a été créée en base de données
        $enseigne->_refresh();
        $this->assertCount(0, $enseigne->getSections(), "Des sections ont été créées malgré l'erreur");
    }

    public function testNotificationsPage(): void
    {
        $client = static::createClient();

        // Créer une enseigne pour tester la page des notifications
        $enseigne = EnseigneFactory::createOne([
            'name' => 'Enseigne Notifications Test',
            'identifier' => 'NOTIFTEST',
            'pole' => Enseigne::POLE_SANTE,
            'formCompleted' => false, // Pas encore terminé
            'sections' => FormSectionFactory::new()->sequence([[
                'title' => 'Informations générales',
                'fields' => FormFieldFactory::new()->sequence([
                    [
                        'label' => 'Adresse e-mail du responsable',
                        'type' => 'managerEmail',
                        'required' => true,
                    ],
                    [
                        'label' => 'FINESS',
                        'type' => 'finess',
                        'required' => true,
                    ],
                ]),
            ]]),
        ]);

        // Accéder directement à la page des notifications
        $client->request('GET', '/admin/enseigne/' . $enseigne->getId() . '/notifications');

        // Vérifier que la page s'affiche correctement
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.substep-title', 'Notifications');

        $form = $client->getCrawler()->selectButton('Terminer')->form();

        // Préparer les données du formulaire
        $formData = [
            'enseigne_notifications' => [
                'contractFilename' => 'contrat_{informations-generales-finess}_{signature-date}',

                'emailSenderEmail' => '<EMAIL>',
                'emailSenderName' => 'Service Adhésion',

                // Notifications Aquitem
                'emailAquitemEnabled' => '1',
                'emailAquitemAttachContract' => '1',
                'emailAquitemRecipientEmail' => '<EMAIL>',
                'emailAquitemSubject' => 'Nouvelle adhésion {informations-generales-finess}',
                'emailAquitemContent' => 'Une nouvelle adhésion a été enregistrée pour {informations-generales-finess}.',

                // Notifications Enseigne
                'emailEnseigneEnabled' => '1',
                'emailEnseigneAttachContract' => '1',
                'emailEnseigneRecipientEmail' => '<EMAIL>',
                'emailEnseigneSubject' => 'Nouvelle adhésion {informations-generales-finess}',
                'emailEnseigneContent' => 'Une nouvelle adhésion a été enregistrée pour {informations-generales-finess}.',

                // Notifications Adhérent
                'emailSubscriberEnabled' => '1',
                'emailSubscriberAttachContract' => '1',
                'emailSubscriberSubject' => 'Confirmation de votre adhésion',
                'emailSubscriberContent' => 'Merci pour votre adhésion à {informations-generales-finess}.',
            ],
        ];

        // Soumettre les données directement
        $client->submit($form, $formData);

        // Vérifier la redirection vers la liste des enseignes
        $this->assertResponseRedirects();
        $client->followRedirect();

        // Vérifier que nous sommes sur la page de liste des enseignes
        $this->assertSelectorTextContains('h3', 'Liste des programmes');

        // Vérifier que les données ont bien été enregistrées
        $enseigne->_refresh();

        // Vérifier que l'enseigne est marquée comme complétée
        $this->assertTrue($enseigne->isFormCompleted(), "L'enseigne n'a pas été marquée comme complétée");

        // Vérifier les données de l'expéditeur
        $this->assertEquals('contrat_{informations-generales-finess}_{signature-date}', $enseigne->getContractFilename());
        $this->assertEquals('<EMAIL>', $enseigne->getEmailSenderEmail());
        $this->assertEquals('Service Adhésion', $enseigne->getEmailSenderName());

        // Vérifier les données des notifications Aquitem
        $this->assertTrue($enseigne->isEmailAquitemEnabled());
        $this->assertTrue($enseigne->isEmailAquitemAttachContract());
        $this->assertEquals('<EMAIL>', $enseigne->getEmailAquitemRecipientEmail());
        $this->assertEquals('Nouvelle adhésion {informations-generales-finess}', $enseigne->getEmailAquitemSubject());
        $this->assertEquals('Une nouvelle adhésion a été enregistrée pour {informations-generales-finess}.', $enseigne->getEmailAquitemContent());

        // Vérifier les données des notifications Enseigne
        $this->assertTrue($enseigne->isEmailEnseigneEnabled());
        $this->assertTrue($enseigne->isEmailEnseigneAttachContract());
        $this->assertEquals('<EMAIL>', $enseigne->getEmailEnseigneRecipientEmail());
        $this->assertEquals('Nouvelle adhésion {informations-generales-finess}', $enseigne->getEmailEnseigneSubject());
        $this->assertEquals('Une nouvelle adhésion a été enregistrée pour {informations-generales-finess}.', $enseigne->getEmailEnseigneContent());

        // Vérifier les données des notifications Adhérent
        $this->assertTrue($enseigne->isEmailSubscriberEnabled());
        $this->assertTrue($enseigne->isEmailSubscriberAttachContract());
        $this->assertEquals('Confirmation de votre adhésion', $enseigne->getEmailSubscriberSubject());
        $this->assertEquals('Merci pour votre adhésion à {informations-generales-finess}.', $enseigne->getEmailSubscriberContent());
    }
}
