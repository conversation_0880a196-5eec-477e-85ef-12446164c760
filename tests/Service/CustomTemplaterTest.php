<?php

namespace App\Tests\Service;

use App\DataFixtures\PlaywrightFixtures;
use App\Service\CustomTemplater;
use App\Service\DataMocker;
use PHPUnit\Framework\TestCase;

class CustomTemplaterTest extends TestCase
{
    public function testRender(): void
    {
        $templater = new CustomTemplater();
        $enseigne = PlaywrightFixtures::getSampleEnseigne();
        $adhesion = DataMocker::mockAdhesion($enseigne);

        foreach ($adhesion->options as $index => $option) {
            $option->option->setId($index);
        }

        $now = date('d/m/Y');

        $this->assertEquals(
            'Test template avec variable : 122222222 12345678910123 ' . $now,
            $templater->render('Test template avec variable : {informations-generales-votre-numero-finess} {informations-generales-siret} {informations-generales-date-souhaitee-d-entree-dans-le-programme}', $enseigne, $adhesion),
        );

        $this->assertEquals(
            'Test options : Welcome Pack : 500 Cartes Privilège 1 Welcome Pack : 500 Cartes Privilège 2',
            $templater->render('Test options : {option-label-0} {option-qtt-0} {option-label-0} {option-qtt-1}', $enseigne, $adhesion),
        );
    }

    public function testGetAvailableTags(): void
    {
        $templater = new CustomTemplater();
        $enseigne = PlaywrightFixtures::getSampleEnseigne();

        foreach ($enseigne->getOptions() as $index => $option) {
            $option->setId($index);
        }

        $availableTags = $templater->getAvailableTags($enseigne);
        $this->assertContains('{option-label-0}', $availableTags);
        $this->assertContains('{option-label-1}', $availableTags);
        $this->assertContains('{option-qtt-0}', $availableTags);
        $this->assertContains('{option-qtt-1}', $availableTags);
    }
}
