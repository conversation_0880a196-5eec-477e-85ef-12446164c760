{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
<div class="card">
    <div class="card-body">
        {{ block('step') }}

        {% if enseigne.askForSepa %}
            <div class="fw-bold mb-3">
                Votre RIB pour prélèvement SEPA
            </div>
        {% endif %}

        {{ form_start(form) }}

        {% if form.iban is defined %}
            {{ form_row(form.iban) }}
        {% endif %}
        {% if form.bic is defined %}
            {{ form_row(form.bic) }}
        {% endif %}

        {% if form.sepaName is defined %}
            {{ form_row(form.sepaName) }}
        {% endif %}
        {% if form.address is defined %}
            {{ form_row(form.address) }}
        {% endif %}
        {% if form.address2 is defined %}
            {{ form_row(form.address2) }}
        {% endif %}
        {% if form.zipCode is defined %}
            {{ form_row(form.zipCode) }}
        {% endif %}
        {% if form.city is defined %}
            {{ form_row(form.city) }}
        {% endif %}
        {% if form.country is defined %}
            {{ form_row(form.country) }}
        {% endif %}

        {{ form_row(form.signaturePhone) }}

        <div class="fw-light mb-4">
            En cliquant sur poursuivre, un code PIN va être envoyé par SMS au numéro spécifié.
            Il sera nécessaire de le renseigner sur la page suivante afin de valider électroniquement votre adhésion.
        </div>

        {% for message in app.flashes('warning') %}
            <div class="alert alert-warning">
                {{ message }}
            </div>
        {% endfor %}

        {% for message in app.flashes('danger') %}
            <div class="alert alert-danger">
                {{ message }}
            </div>
        {% endfor %}

        <div class="text-center mb-4">
            <a href="{{ url('app_adhesion_step_' ~ (step - 1), {identifier: enseigne.identifier}) }}" class="btn btn-primary">Retour</a>
            <input type="submit" class="btn btn-secondary" value="Poursuivre"/>
        </div>

        {{ form_end(form) }}

        <div class="form-text fst-italic">* Champs obligatoires</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('adhesion-sepa') }}/>
{% endblock %}
