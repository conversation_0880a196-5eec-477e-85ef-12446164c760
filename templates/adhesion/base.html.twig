{% extends 'base.html.twig' %}

{% block title %}Adhésion au programme {{ enseigne.name }}{% endblock %}

{% block header_title %}Adhésion au programme {{ enseigne.name }}{% endblock %}

{% block container_class %}{% endblock %}

{% block styles %}
    <style>
        :root {
            --bs-primary: {{ enseigne.color(1) }};
            --bs-primary-rgb: {{ enseigne.color(1, 'rgb') }};
            --bs-secondary: {{ enseigne.color(2) }};
            --bs-secondary-rgb: {{ enseigne.color(2, 'rgb') }};
        }
    </style>
{% endblock %}

{% block step %}
    <div class="d-flex align-items-center justify-content-center mb-4">
        <div class="summary-dot">{{ step }}</div>
        <div class="fw-bold">{{ stepManager.step(enseigne, step) }}</div>
    </div>
{% endblock %}

{% block body %}
    <div class="bg-white text-center py-2">
        <img id="logo-preview" height="129px" src="{{ asset(uploadsPath ~ '/' ~ enseigne.logo) }}" alt="{{ enseigne.name }}">
    </div>
{# <turbo-frame id="adhesion-form"> #}
        <div class="container container-adhesion mt-4 px-3">
            {% block sub_body %}{% endblock %}
        </div>
{# </turbo-frame> #}
{% endblock %}
