{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
<div class="card">
    <div class="card-body">
        {{ block('step') }}

        {{ form_start(form) }}

        <div class="mb-5">
            {% if enseigne.options|length %}
                {% for option in enseigne.options %}
                    <div class="row">
                        <div class="mb-2">
                            {{ form_errors(form.options[loop.index0].quantity) }}
                        </div>
                        <div class="col-3 col-sm-2">
                            {{ form_widget(form.options[loop.index0].quantity) }}
                        </div>
                        <div class="col-9 col-sm-7">
                            <div class="fw-bold">{{ option.name }}</div>
                            <div>{{ option.text }}</div>
                        </div>
                        {% if not option.noPriceDisplayed %}
                            <div class="col-sm-3 text-end mt-3 mt-sm-0" id="price-{{ option.id }}">
                                {% if option.isFree or option.unitPrice is null %}
                                    OFFERT
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                    {% if not loop.last %}
                        <hr>
                    {% endif %}
                {% endfor %}
            {% else %}
                <div class="mt-5 mb-5 text-center">
                    Aucune option disponible
                </div>
                <div class="d-none">
                    {{ form_row(form.options) }}
                </div>
            {% endif %}

        </div>

        {{ form_rest(form) }}

        <div class="text-center">
            <a href="{{ url('app_adhesion_step_' ~ (step - 1), {identifier: enseigne.identifier}) }}" class="btn btn-primary">Retour</a>
            <input type="submit" class="btn btn-secondary" value="Poursuivre"/>
        </div>

        {{ form_end(form) }}

    </div>
</div>
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('adhesion-options') }}/>
{% endblock %}
