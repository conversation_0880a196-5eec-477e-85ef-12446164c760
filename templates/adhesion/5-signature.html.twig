{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
<div class="card">
    <div class="card-body">
        {{ block('step') }}

        <div class="fw-bold mb-3">
            Votre contrat
        </div>

        <div class="mb-3">
            {{ enseigne.textBeforeSignature|emailLink|nl2br }}
        </div>

        <div class="w-100 mb-5" style="height: 600px;" {{ stimulus_controller('pdf-viewer', {
            pdfUrl: url('app_pdf_contract', {
                identifier: enseigne.identifier,
            }),
        }) }}></div>

        {{ form_start(form) }}

        <hr>
        <div class="fw-bold mb-3">
            Conditions générales de vente
        </div>

        {{ form_row(form.cgvAccepted) }}
        {% if form.sepaAccepted is defined %}
            {{ form_row(form.sepaAccepted) }}
        {% endif %}
        {% if form.debitAccepted is defined %}
            {{ form_row(form.debitAccepted) }}
        {% endif %}

        <hr>
        <div class="fw-bold mb-3">
            Signature du responsable
        </div>

        <div class="signature-pad mb-4">
            <div class="d-flex align-items-center justify-content-between mb-2">
                {{ form_label(form.signatureData) }}
                <div role="button" id="signature-clear">
                    <i class="fa-solid fa-rotate-right"></i>
                    Effacer
                </div>
            </div>

            <div class="signature-pad-body">
                <div id="signature-icon">
                    <i class="fa-light fa-signature"></i>
                    <i class="fa-light fa-pen"></i>
                </div>
                <canvas id="signature-pad"></canvas>
            </div>
            {{ form_errors(form.signatureData) }}
            {{ form_widget(form.signatureData) }}
        </div>

        <div class="mb-5">
            <div class="d-flex flex-row align-items-center justify-content-center gap-3">
                <label for="" class="required">Saisissez le code PIN reçu par SMS au {{ adhesion.sepa.signaturePhone|replace({'+33': '0'}) }}</label>
                {{ form_widget(form.pin, {
                    attr: {style: 'width: 100px'},
                }) }}
            </div>
            <div class="text-center">
                {{ form_errors(form.pin) }}
            </div>
        </div>

        <div class="text-center mb-4">
            <a href="{{ url('app_adhesion_step_' ~ (step - 1), {identifier: enseigne.identifier}) }}" class="btn btn-primary">Retour</a>
            <input type="submit" class="btn btn-secondary" value="Signer mon contrat"/>
        </div>

        {{ form_end(form) }}

        <div class="form-text fst-italic">* Champs obligatoires</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('adhesion-signature') }}/>
{% endblock %}
