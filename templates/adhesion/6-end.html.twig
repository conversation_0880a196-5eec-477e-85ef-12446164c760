{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
<div class="card">
    <div class="card-body">
        <div class="fw-bold mb-3">
            Votre contrat d'adhésion est signé
        </div>

        <div class="mb-3">
            {{ enseigne.textAfterSignature|emailLink|nl2br }}
        </div>

        <div class="fw-bold mb-3 text-center">
            Contrat {{ enseigne.getContractName(adhesion) }}
        </div>

        <div class="mb-5 text-center">
            <a target="_blank" href="{{ url('app_pdf_contract', {identifier: enseigne.identifier}) }}" class="btn btn-primary btn-sm">
                <i class="fa-regular fa-download"></i>
                Télécharger mon contrat signé
            </a>
        </div>

        <div class="mb-3">
             Un exemplaire du contrat a été envoyé par mail à l'adresse <a href="mailto:{{ adhesion.infos.getRequiredValue(enseigne, 'managerEmail') }}">{{ adhesion.infos.getRequiredValue(enseigne, 'managerEmail') }}</a>
        </div>

        <div class="mb-3">
            {{ enseigne.textContact|emailLink|nl2br }}
        </div>

    </div>
</div>
{% endblock %}
