{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
<div class="card">
    <div class="card-body">
        <p class="fw-bold">
            Rejoindre le programme {{ enseigne.name }}
        </p>
        <div class="mb-3" data-test-id="presentation">
            {{ enseigne.textPresentation|emailLink|nl2br }}
        </div>

        <div class="mb-4 ms-sm-5">
            {% for step in stepManager.steps(enseigne) %}
                <div class="d-flex align-items-center mb-2">
                    <div class="summary-dot">{{ loop.index }}</div>
                    <div class="fw-bold">{{ step }}</div>
                </div>
            {% endfor %}
        </div>

        <div class="text-center mb-4">
            <a href="{{ url('app_adhesion_step_1', {identifier: enseigne.identifier}) }}" class="btn btn-secondary">Commencer</a>
        </div>

        <div>
            {{ enseigne.textContact|emailLink|nl2br }}
        </div>
    </div>
</div>
{% endblock %}
