{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
    <div class="card">
        <div class="card-body">
            {{ block('step') }}

            {{ form_start(form) }}

            {% for section in enseigne.sections %}
                <hr>
                <div class="mb-3">
                    <div class="fw-bold">
                        {{ section.title }}
                    </div>
                    {% if section.description %}
                        <div class="form-text mb-3">{{ section.description }}</div>
                    {% endif %}
                </div>

                {% for field in section.fields %}
                    {{ form_row(form[field.name]) }}
                {% endfor %}
            {% endfor %}

            {{ form_rest(form) }}

            <div class="mt-4 mb-3">
                {{ enseigne.textUnderLGO|emailLink|nl2br }}
            </div>

            <div class="text-center mb-4">
                <a href="{{ url('app_adhesion_step_' ~ (step - 1), {identifier: enseigne.identifier}) }}" class="btn btn-primary">Retour</a>
                <input type="submit" class="btn btn-secondary" value="Poursuivre"/>
            </div>

            {{ form_end(form) }}

            <div class="form-text fst-italic">* Champs obligatoires</div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
{% endblock %}
