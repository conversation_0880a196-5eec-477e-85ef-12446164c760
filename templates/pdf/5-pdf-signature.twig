<h2>Contrat {{ enseigne.contractName(adhesion) }}</h2>

{% macro check() %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z"/></svg>
{% endmacro %}

{% macro checkbox() %}
    <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 535 531">
        <g transform="translate(0 -521.36)">
            <path style="stroke-linejoin:round;stroke:#000000;stroke-width:55;fill:none" d="m139.59 551.39h252.13c61.025 0 110.15 44.742 110.15 100.32v271.8c0 55.577-49.129 100.32-110.15 100.32h-252.13c-61.025 0-110.15-44.742-110.15-100.32v-271.8c0-55.577 49.129-100.32 110.15-100.32z"/>
        </g>
    </svg>
{% endmacro %}

{% macro lock() %}
    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="40" viewBox="0 0 93.63 122.88"><path fill="#fbd734" d="M6,47.51H87.64a6,6,0,0,1,6,6v63.38a6,6,0,0,1-6,6H6a6,6,0,0,1-6-6V53.5a6,6,0,0,1,6-6Z"/><path fill="#36464e" class="cls-2" d="M41.89,89.26l-6.47,16.95H58.21L52.21,89a11.79,11.79,0,1,0-10.32.24Z"/><path fill="#36464e" class="cls-2" d="M83.57,47.51H72.22V38.09a27.32,27.32,0,0,0-7.54-19,24.4,24.4,0,0,0-35.73,0,27.32,27.32,0,0,0-7.54,19v9.42H10.06V38.09A38.73,38.73,0,0,1,20.78,11.28a35.69,35.69,0,0,1,52.07,0A38.67,38.67,0,0,1,83.57,38.09v9.42Z"/></svg>
{% endmacro %}

{% macro validatedLock(adhesion) %}
    {% if adhesion.signature.signed and adhesion.signature.pinSentDate is defined %}
        <div class="row mb-5" style="display: -webkit-box">
            <div class="col-auto me-3 pe-5">
                {{ _self.lock }}
            </div>
            <div>
                <b>Validé par signature électronique le {{ adhesion.signature.pinSentDate|date('d/m/Y') }} à {{ adhesion.signature.pinSentDate|date('H:i') }} via SMS envoyé
                    au {{ adhesion.sepa.signaturePhone }}</b>
            </div>
        </div>
    {% endif %}
{% endmacro %}

<div class="inner">

    <br>
    <br>
    <br>

    <div class="row mb-5" style="display: -webkit-box">
        <div class="col-auto me-3 pe-4">
            {% if adhesion.signature.cgvAccepted is defined and adhesion.signature.cgvAccepted %}
                {{ _self.check }}
            {% else %}
                {{ _self.checkbox }}
            {% endif %}
        </div>
        <div>
            {{ constant('App\\Form\\AdhesionSignatureType::TEXT_CGV') }}
        </div>
    </div>

    {% if enseigne.askForSepa %}
        <div class="row mb-5" style="display: -webkit-box">
            <div class="col-auto me-3 pe-4">
                {% if adhesion.signature.cgvAccepted is defined and adhesion.signature.sepaAccepted %}
                    {{ _self.check }}
                {% else %}
                    {{ _self.checkbox }}
                {% endif %}
            </div>
            <div>
                {{ constant('App\\Form\\AdhesionSignatureType::TEXT_SEPA') }}
            </div>
        </div>

        <div class="row mb-5" style="display: -webkit-box">
            <div class="col-auto me-3 pe-4">
                {% if adhesion.signature.cgvAccepted is defined and adhesion.signature.debitAccepted %}
                    {{ _self.check }}
                {% else %}
                    {{ _self.checkbox }}
                {% endif %}
            </div>
            <div>
                {{ constant('App\\Form\\AdhesionSignatureType::TEXT_DEBIT') }}
            </div>
        </div>
    {% endif %}

    <div class="mb-5 text-end">
        <div class="mb-3" >
            {% if adhesion.signature.signatureData is defined %}
                <img style="width: 250px;" src="{{ adhesion.signature.signatureData }}" />
            {% else %}
                <div style="margin-bottom: 30px">En attente de signature</div>
            {% endif %}
        </div>
        {% for field in enseigne.getFieldsForPdfSection('signature') %}
            <div>{{ adhesion.infos.getDynamicValueFormatted(field.name) }}</div>
        {% endfor %}
    </div>

    {{ _self.validatedLock(adhesion) }}
</div>
