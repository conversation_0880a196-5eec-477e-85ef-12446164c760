<h2>Prélèvement SEPA</h2>

{% set spacing = 46 %}
{% set rightX = 305 %}

{% set nameY = 0 %}
{% set addressY = spacing %}
{% set zipCodeY = spacing * 2 %}
{% set cityY = spacing * 2 %}
{% set countryY = spacing * 3 %}
{% set ibanY = spacing * 4 %}
{% set bicY = spacing * 5 %}
{% set citySignY = 470 %}
{% set dateY = citySignY %}

{% set signY = 15 %}

<div class="position-relative mb-5" style="margin-top: 135px; height: 897px; border: 1px solid transparent">
    <div class="position-absolute" style="left: 208px; top: 165px; font-weight: bold; font-size: 14px">
        <div class="position-relative" style="width: 680px; height: 550px; border: 1px solid transparent">
            <div class="position-absolute" style="top: {{ nameY }}px">
                {{ adhesion.sepa.sepaName }}
            </div>
            <div class="position-absolute" style="width: 680px; top: {{ addressY }}px">
                {{ adhesion.sepa.address }}, {{ adhesion.sepa.address2 }}
            </div>
            <div class="position-absolute" style="top: {{ zipCodeY }}px">
                {{ adhesion.sepa.zipCode }}
            </div>
            <div class="position-absolute" style="left: {{ rightX }}px; top: {{ cityY }}px">
                {{ adhesion.sepa.city }}
            </div>
            <div class="position-absolute" style="top: {{ countryY }}px">
                {{ adhesion.sepa.countryName }}
            </div>
            <div class="position-absolute" style="top: {{ ibanY }}px">
                {{ adhesion.sepa.ibanFormatted }}
            </div>
            <div class="position-absolute" style="top: {{ bicY }}px">
                {{ adhesion.sepa.bic }}
            </div>
            <div class="position-absolute" style="top: {{ dateY }}px">
                {{ adhesion.sepa.city }}
            </div>
            <div class="position-absolute" style="left: {{ rightX }}px; top: {{ dateY }}px">
                {{ adhesion.signature.signatureDate|default('now')|date('d/m/Y') }}
            </div>
        </div>
    </div>
    <div class="position-absolute" style="right: 90px; bottom: 70px">
        {% if adhesion.signature.signatureData is defined %}
            <img height="125px" src="{{ adhesion.signature.signatureData }}"/>
        {% else %}
            <div style="width: 300px; height: 72px">En attente de signature</div>
        {% endif %}
    </div>
</div>

{% import 'pdf/5-pdf-signature.twig' as signature %}

<div class="inner">
    {{ signature.validatedLock(adhesion) }}
</div>
