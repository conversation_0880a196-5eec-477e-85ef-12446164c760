<h2>Vos options & packs</h2>

<div class="inner" style="margin-top: 100px;">
    <div class="mb-5">
        {% for adhesionOption in adhesion.options %}
            {% if adhesionOption.option %}
                <div style="page-break-inside: avoid">
                    <div class="row mb-5" style="display: -webkit-box;">
                        <div class="col-1">
                            {{ adhesionOption.quantity }}
                        </div>
                        <div class="col-9" style="{{ adhesionOption.quantity == 0 ? 'text-decoration: line-through' : '' }}">
                            <div class="fw-bold mb-3">{{ adhesionOption.option.name }}</div>
                            <div>{{ adhesionOption.option.text }}</div>
                        </div>
                        <div class="col-2 text-end mt-3 mt-sm-0">
                            {% if adhesionOption.option.isFree or adhesionOption.option.unitPrice is null %}
                                OFFERT
                            {% else %}
                                {{ adhesionOption.price }}€ HT
                            {% endif %}
                        </div>
                    </div>
                    {% if not loop.last %}
                        <div class="delimiter"></div>
                    {% endif %}
                </div>
            {% endif %}
        {% endfor %}
    </div>
</div>
