{% apply inline_css(encore_entry_css_source('bootstrap'), encore_entry_css_source('pdf')) %}
<!doctype html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <style>
        h1, h2, h3, .delimiter {
            color: {{ enseigne.color(3) }};
            border-color: {{ enseigne.color(3) }};
        }
    </style>
</head>
<body>
<div class="page">
    {{ include('pdf/1-pdf-summary.twig') }}
</div>
<div class="page">
    {{ include('pdf/2-pdf-infos.twig') }}
</div>
<div class="page">
    {{ include('pdf/3-pdf-options.twig') }}
</div>
<div class="page page-no-break">
    {{ include('pdf/5-pdf-signature.twig') }}
</div>
</body>
</html>
{% endapply %}
