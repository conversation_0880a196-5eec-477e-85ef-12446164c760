{% block intl_tel_input_widget %}
    {{ dump() }}
    <div class="intl-tel-input-wrapper" {{ stimulus_controller('intl-tel-input', {
        onlyCountries: form.vars.value.options.dialCodes ?? ['+33'],
    }) }}>
        <input
                id="{{ id }}___intl"
                type="tel"
                data-intl-tel-input-target="input"
                autocomplete="off"
                class="form-control"
                {% if required %}required{% endif %}
        >
        {{ form_widget(form, {attr: {
            'data-intl-tel-input-target': 'hidden',
            autocomplete: 'off',
        }}) }}
    </div>
{% endblock %}
