{% block geo_autocomplete_widget %}
    <div class="geo-autocomplete-wrapper" data-controller="geo-autocomplete">
        {{ form_widget(form, {attr: {
            'data-geo-autocomplete-target': 'input',
            'data-geo-autocomplete-type-value': 'municipality',
            autocomplete: 'off',
        }}) }}
        <div data-geo-autocomplete-target="results" class="geo-results d-none"></div>
    </div>
{% endblock %}
