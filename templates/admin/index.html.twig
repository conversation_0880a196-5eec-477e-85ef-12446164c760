{% extends 'base.html.twig' %}

{% block title %}Liste des programmes{% endblock %}

{% block body %}
<div class="card">
    <div class="card-body">
        <h3>Liste des programmes :</h3>
        <ul class="list-group mt-3 mb-3">
        {% for enseigne in enseignes %}
            <li class="list-group-item d-flex align-items-center justify-content-between">
                <div>
                    {% if enseigne.formCompleted %}
                        <a target="_blank" href="{{ path('app_adhesion_summary', {identifier: enseigne.identifier}) }}">{{ enseigne.name }}</a>
                    {% else %}
                        {{ enseigne.name }} <span class="fst-italic">(En cours de création)</span>
                    {% endif %}
                </div>
                <div>
                    <a href="{{ path('app_admin_enseigne_edit', {id: enseigne.id}) }}" class="btn btn-primary">Modifier</a>
                    <a href="{{ path('app_admin_enseigne_delete', {id: enseigne.id}) }}" class="btn btn-danger" onclick="return confirm('Êtes-vous sur de vouloir supprimer l\'enseigne {{ enseigne.name }}')">Supprimer</a>
                </div>
            </li>
        {% endfor %}
        </ul>
        <div class="text-center">
            <a href="{{ path('app_admin_enseigne_create') }}" class="btn btn-primary">Ajouter un programme</a>
        </div>
    </div>
</div>
{% endblock %}
