{% extends 'base.html.twig' %}

{% block body %}
    <div class="mb-3">
        <a class="btn btn-secondary" href="{{ path('app_admin_enseigne_liste') }}">Retour à la liste des enseignes</a>
    </div>

    {# Navigation des étapes #}
    <div class="steps-navigation d-flex justify-content-between mb-4 gap-3">
        <div class="main-step {% if current_step >= 1 %}completed{% endif %} {% if current_step == 1 %}active{% endif %}">
            <span class="step-number">1</span>
            <span class="step-title">Informations générales</span>
        </div>
        <div class="main-step {% if current_step >= 2 %}completed{% endif %} {% if current_step == 2 %}active{% endif %}">
            <span class="step-number">2</span>
            <span class="step-title">Formulaire</span>
        </div>
        <div class="main-step {% if current_step >= 3 %}completed{% endif %} {% if current_step == 3 %}active{% endif %}">
            <span class="step-number">3</span>
            <span class="step-title">Notifications</span>
        </div>
    </div>

    <div class="d-flex">
        {% block sidebar %}
            {% if current_step == 1 %}
                <div class="substeps-sidebar card me-3">
                    <div class="fs-5 fw-bold mb-3">Étapes</div>
                    <div class="substep {% if current_substep == 1 %}active{% endif %} {% if current_substep > 1 %}completed{% endif %}">
                        <i class="fas fa-pen"></i>
                        <span>Identification</span>
                    </div>
                    <div class="substep {% if current_substep == 2 %}active{% endif %} {% if current_substep > 2 %}completed{% endif %}">
                        <i class="fas fa-list-check"></i>
                        <span>Options</span>
                    </div>
                    <div class="substep {% if current_substep == 3 %}active{% endif %} {% if current_substep > 3 %}completed{% endif %}">
                        <i class="fas fa-palette"></i>
                        <span>Personnalisation</span>
                    </div>
                    <div class="substep {% if current_substep == 4 %}active{% endif %} {% if current_substep > 4 %}completed{% endif %}">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>Facturation</span>
                    </div>
                </div>
            {% endif %}
        {% endblock %}

        {# Contenu principal #}
        <div class="flex-grow-1">
            <div class="card">
                <div class="card-body">
                    {% block form %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .steps-navigation {
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .main-step {
            color: #ADADAD;
            display: flex;
            flex: 0 1 auto;
            width: 100%;
            align-items: center;
            gap: 0.5rem;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            border-bottom: 8px solid #ADADAD;
            padding-bottom: 0.5rem;
        }

        .main-step.completed, .main-step.active {
            color: var(--bs-primary);
            border-bottom-color: var(--bs-primary);
        }

        .main-step .step-number {
            font-weight: bold;
        }

        .substeps-sidebar {
            width: 256px;
            padding: 1rem;
        }

        .substep {
            margin-bottom: 1.5rem;
            color: #ADADAD;
            font-size: 1rem;
            font-style: normal;
            font-weight: 600;
            line-height: 21px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .substep.active {
            color: var(--bs-primary);
        }

        .substep.completed {
            color: #198754;
        }

        .substep i {
            width: 20px;
            text-align: center;
        }
    </style>
{% endblock %}
