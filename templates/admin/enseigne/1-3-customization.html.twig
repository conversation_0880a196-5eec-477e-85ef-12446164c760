{% extends 'admin/enseigne/base.html.twig' %}

{% block form %}
    <div class="substep-title fs-5 fw-bold mb-3">Personnalisation</div>

    {{ form_start(form) }}

    {{ form_row(form.textPresentation) }}
    {{ form_row(form.textUnderLGO) }}
    {{ form_row(form.textBeforeSignature) }}
    {{ form_row(form.textAfterSignature) }}
    {{ form_row(form.textContact) }}

    <div class="mb-3">
        {{ form_label(form.color1) }}
        {{ form_errors(form.color1) }}
        <div class="d-flex align-items-center">
            <div id="color-1-preview" class="me-2 rounded-1" style="width: 30px; height: 30px; background: {{ enseigne.color1 }}"></div>
            <div>
                {{ form_widget(form.color1) }}
            </div>
        </div>
    </div>

    <div class="mb-3">
        {{ form_label(form.color2) }}
        {{ form_errors(form.color2) }}
        <div class="d-flex align-items-center">
            <div id="color-2-preview" class="me-2 rounded-1" style="width: 30px; height: 30px; background: {{ enseigne.color2 }}"></div>
            <div>
                {{ form_widget(form.color2) }}
            </div>
        </div>
    </div>

    <div class="mb-3">
        {{ form_label(form.color3) }}
        {{ form_errors(form.color3) }}
        <div class="d-flex align-items-center">
            <div id="color-3-preview" class="me-2 rounded-1" style="width: 30px; height: 30px; background: {{ enseigne.color3 }}"></div>
            <div>
                {{ form_widget(form.color3) }}
            </div>
        </div>
    </div>

    {{ form_row(form.linkMobilityAccp) }}

    <div class="d-flex justify-content-between mt-4">
        <a href="{{ path('app_admin_enseigne_1_options', {id: enseigne.id}) }}" class="btn btn-secondary">Précédent</a>
        <button type="submit" class="btn btn-primary">Suivant</button>
    </div>

    {{ form_end(form) }}
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('enseigne-customization') }}/>
    <div {{ stimulus_controller('form-errors') }}/>
{% endblock %}
