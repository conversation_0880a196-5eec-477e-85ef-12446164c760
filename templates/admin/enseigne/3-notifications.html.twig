{% extends 'admin/enseigne/base.html.twig' %}

{% block form %}
    <div class="substep-title fs-5 fw-bold mb-3">Notifications</div>

    {{ form_start(form) }}

    <div class="mb-3">
        {{ form_label(form.contractFilename) }}
        {{ form_errors(form.contractFilename) }}
        <div class="d-flex align-items-center">
            <div class="me-1">
                <div class="input-group mb-3">
                    {{ form_widget(form.contractFilename) }}
                    <span class="input-group-text">.pdf</span>
                </div>
                {{ form_help(form.contractFilename) }}
            </div>
        </div>
    </div>

    {{ form_row(form.emailSenderEmail) }}
    {{ form_row(form.emailSenderName) }}

    <div class="mt-5 mb-3 d-flex align-items-center">
        <div class="flex-grow-1 fw-bold">Notifications mail pour Aquitem</div>
        {{ form_row(form.emailAquitemEnabled) }}
        {{ form_row(form.emailAquitemAttachContract) }}
    </div>

    {{ form_row(form.emailAquitemRecipientEmail) }}
    {{ form_row(form.emailAquitemSubject) }}
    {{ form_row(form.emailAquitemContent) }}

    <div class="mt-5 mb-3 d-flex align-items-center">
        <div class="flex-grow-1 fw-bold">Notifications mail pour le siège</div>
        {{ form_row(form.emailEnseigneEnabled) }}
        {{ form_row(form.emailEnseigneAttachContract) }}
    </div>

    {{ form_row(form.emailEnseigneRecipientEmail) }}
    {{ form_row(form.emailEnseigneSubject) }}
    {{ form_row(form.emailEnseigneContent) }}

    <div class="mt-5 mb-3 d-flex align-items-center">
        <div class="flex-grow-1 fw-bold">Notifications mail pour l'adhérent</div>
        {{ form_row(form.emailSubscriberEnabled) }}
        {{ form_row(form.emailSubscriberAttachContract) }}
    </div>

    {{ form_row(form.emailSubscriberSubject) }}
    {{ form_row(form.emailSubscriberContent) }}

    <div class="d-flex justify-content-between mt-4">
        <a href="{{ path('app_admin_enseigne_2_form', {id: enseigne.id}) }}" class="btn btn-secondary">Précédent</a>
        <button type="submit" class="btn btn-primary">Terminer</button>
    </div>

    {{ form_end(form) }}
{% endblock %}
