{% extends 'admin/enseigne/base.html.twig' %}

{% block form %}
    <div class="substep-title fs-5 fw-bold mb-3">Identification de l'enseigne</div>

    {{ form_start(form) }}

    {{ form_row(form.name) }}

    <div class="mb-3">
        {{ form_label(form.logoFile) }}
        <div class="mb-3 {% if enseigne.logo is null %}d-none{% endif %}">
            <img id="logo-preview" class="p-3 border border-1 border-dark" style="--bs-border-opacity: .25;" {% if enseigne.logo %}src="{{ asset(uploadsPath ~ '/' ~ enseigne.logo) }}"{% endif %} alt="">
        </div>
        {{ form_widget(form.logoFile) }}
        {{ form_errors(form.logoFile) }}
    </div>

    {{ form_row(form.identifier) }}

    <div class="mb-3">
        {{ form_label(form.contractFile) }}
        <div class="mb-3 {% if enseigne.contract is null %}d-none{% endif %}">
            <a class="btn btn-primary" target="_blank" href="{{ asset(uploadsPath ~ '/' ~ enseigne.contract) }}">Voir</a>
        </div>
        {{ form_widget(form.contractFile) }}
        {{ form_errors(form.contractFile) }}
    </div>

    {{ form_row(form.pole) }}

    <div class="d-flex justify-content-between mt-4">
        <a href="{{ path('app_admin_enseigne_liste') }}" class="btn btn-secondary">Annuler</a>
        <button type="submit" class="btn btn-primary">Suivant</button>
    </div>

    {{ form_end(form) }}
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('enseigne-identification') }}/>
    <div {{ stimulus_controller('form-errors') }}/>
{% endblock %}
