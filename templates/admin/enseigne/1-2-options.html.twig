{% extends 'admin/enseigne/base.html.twig' %}

{% import _self as formMacros %}

{% macro collection_item(form) %}
    <div class="ps-3 mb-3 border-start border-info border-3 d-flex" data-form-collection-target="field">
        <div role="button" class="handle">
            <i class="fa fa-grip-dots fa-handle fs-3 me-3"></i>
        </div>
        <div class="flex-grow-1">
            {{ form_row(form.name) }}
            {{ form_row(form.text) }}
            <div class="row row-cols-lg-auto g-3 align-items-center">
                {{ form_row(form.hasQuantity) }}
                {{ form_row(form.isFree) }}
                {{ form_row(form.noPriceDisplayed) }}
                {{ form_row(form.unitPrice) }}
            </div>
            {{ form_row(form.position) }}
            {{ form_rest(form) }}
            {% if form.vars.value.id|default(false) %}
                <div class="form-text mb-3">Variable mail : {option-{{ form.vars.value.id }}}</div>
            {% endif %}
            <button class="btn btn-danger" type="button" data-action="form-collection#removeItem">Supprimer</button>
        </div>
    </div>
{% endmacro %}

{% block form %}
    <div class="substep-title fs-5 fw-bold mb-3">Options</div>

    {{ form_start(form) }}

    {{ form_label(form.options) }}
    <div class="mb-3" {{ stimulus_controller('form-collection', {
        value: form.options|length > 0 ? form.options|last.vars.name + 1 : 0,
        prototype: formMacros.collection_item(form.options.vars.prototype)|json_encode,
    }) }}>
        <div id="options-list" data-form-collection-target="fields">
            {% do form.options.setRendered %}
            {% for field in form.options %}
                {{ formMacros.collection_item(field) }}
            {% endfor %}
        </div>
        <button class="btn btn-primary" type="button"
                data-action="form-collection#addItem"
                data-form-collection-target="addButton">
            Ajouter une option
        </button>
    </div>

    <div class="d-flex justify-content-between mt-4">
        <a href="{{ path('app_admin_enseigne_1_identification', {id: enseigne.id}) }}" class="btn btn-secondary">Précédent</a>
        <button type="submit" class="btn btn-primary">Suivant</button>
    </div>

    {{ form_end(form) }}
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('enseigne-options') }}/>
    <div {{ stimulus_controller('form-errors') }}/>
{% endblock %}
