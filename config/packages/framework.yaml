# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    trusted_proxies: '%env(TRUSTED_PROXIES)%'
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    http_method_override: false
    handle_all_throwables: true

    # Note that the session will be started ONLY if you read or write from it.
    session: true

    #esi: true
    #fragments: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
