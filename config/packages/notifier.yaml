framework:
    notifier:
        chatter_transports:
        texter_transports:
        channel_policy:
            urgent: ['sms']
            high: ['sms']
            medium: ['sms']
            low: ['sms']
        admin_recipients:
            - { email: <EMAIL> }

when@dev:
    framework:
        notifier:
            texter_transports:
                fakesms: '%env(FAKE_SMS_DSN)%'

when@prod:
    framework:
        notifier:
            texter_transports:
                linkmobility-sms: '%env(LINKMOBILITY_DSN)%'

when@test:
    framework:
        notifier:
            texter_transports:
              fakesms: 'null://null'