services:
    php:
        image: gitlab.alienor.net:5050/dev-projets-aquitem/demat-adhesion:dev
        environment:
            SERVER_NAME: ${SERVER_NAME:-localhost}, php:80
            TRUSTED_PROXIES: ${TRUSTED_PROXIES:-*********/8,10.0.0.0/8,**********/12,***********/16}
            TRUSTED_HOSTS: ${TRUSTED_HOSTS:-^${SERVER_NAME:-example\.com|localhost}|php$$}
        depends_on:
          database:
            condition: service_healthy
        volumes:
            - caddy_data:/data
            - caddy_config:/config
        ports:
            # HTTP
            - target: 80
              published: ${HTTP_PORT:-80}
              protocol: tcp
            # HTTPS
            - target: 443
              published: ${HTTPS_PORT:-443}
              protocol: tcp
            # HTTP/3
            - target: 443
              published: ${HTTP3_PORT:-443}
              protocol: udp
    
    ###> doctrine/doctrine-bundle ###
    database:
        image: mariadb:10.4.7
        command:
          - "--default-authentication-plugin=mysql_native_password"
          - "--lower_case_table_names=1"
        environment:
            - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root}
            - MYSQL_DATABASE=${MYSQL_DATABASE:-anetdb}
            - MYSQL_USER=${MYSQL_USER:-userdb}
            - MYSQL_PASSWORD=${MYSQL_PASSWORD:-passdb}
        volumes:
            - db_data:/var/lib/mysql
        healthcheck:
          test: ["CMD", 'mysqladmin', 'ping', '-h', '127.0.0.1', '-u', 'root', '-proot' ]
          timeout: 5s
          retries: 5
          start_period: 60s
    ###< doctrine/doctrine-bundle ###

volumes:
    caddy_data:
    caddy_config:
    db_data:
