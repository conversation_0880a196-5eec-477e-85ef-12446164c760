<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221107125836 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE enseigne ADD logo VARCHAR(255) NOT NULL, ADD contract VARCHAR(255) NOT NULL, ADD identifier VARCHAR(10) NOT NULL, ADD text_presentation LONGTEXT DEFAULT NULL, ADD text_under_lgo LONGTEXT DEFAULT NULL, ADD text_before_signature LONGTEXT DEFAULT NULL, ADD text_after_signature LONGTEXT DEFAULT NULL, ADD text_contact LONGTEXT DEFAULT NULL, ADD color1 VARCHAR(7) DEFAULT NULL, ADD color2 VARCHAR(7) DEFAULT NULL, ADD email_sender_email VARCHAR(255) NOT NULL, ADD email_sender_name VARCHAR(255) NOT NULL, ADD email_aquitem_enabled TINYINT(1) NOT NULL, ADD email_aquitem_attach_contract TINYINT(1) NOT NULL, ADD email_aquitem_recipient_email LONGTEXT DEFAULT NULL, ADD email_aquitem_subject VARCHAR(255) DEFAULT NULL, ADD email_aquitem_content LONGTEXT DEFAULT NULL, ADD email_enseigne_enabled TINYINT(1) NOT NULL, ADD email_enseigne_attach_contract TINYINT(1) NOT NULL, ADD email_enseigne_recipient_email LONGTEXT DEFAULT NULL, ADD email_enseigne_subject VARCHAR(255) DEFAULT NULL, ADD email_enseigne_content LONGTEXT DEFAULT NULL, ADD email_subscriber_enabled TINYINT(1) NOT NULL, ADD email_subscriber_attach_contract TINYINT(1) NOT NULL, ADD email_subscriber_subject VARCHAR(255) DEFAULT NULL, ADD email_subscriber_content LONGTEXT DEFAULT NULL, ADD contract_filename VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE enseigne DROP logo, DROP contract, DROP identifier, DROP text_presentation, DROP text_under_lgo, DROP text_before_signature, DROP text_after_signature, DROP text_contact, DROP color1, DROP color2, DROP email_sender_email, DROP email_sender_name, DROP email_aquitem_enabled, DROP email_aquitem_attach_contract, DROP email_aquitem_recipient_email, DROP email_aquitem_subject, DROP email_aquitem_content, DROP email_enseigne_enabled, DROP email_enseigne_attach_contract, DROP email_enseigne_recipient_email, DROP email_enseigne_subject, DROP email_enseigne_content, DROP email_subscriber_enabled, DROP email_subscriber_attach_contract, DROP email_subscriber_subject, DROP email_subscriber_content, DROP contract_filename');
    }
}
