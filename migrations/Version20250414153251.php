<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250414153251 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE form_field (id INT AUTO_INCREMENT NOT NULL, section_id INT NOT NULL, name VARCHAR(255) NOT NULL, label VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, required TINYINT(1) NOT NULL, options JSON DEFAULT NULL COMMENT '(DC2Type:json)', help LONGTEXT DEFAULT NULL, position INT NOT NULL, validation_rules VARCHAR(255) DEFAULT NULL, INDEX IDX_D8B2E19BD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE form_section (id INT AUTO_INCREMENT NOT NULL, enseigne_id INT NOT NULL, name VARCHAR(255) NOT NULL, title VARCHAR(255) DEFAULT NULL, position INT NOT NULL, INDEX IDX_1C1F1AD46C2A0A71 (enseigne_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE form_field ADD CONSTRAINT FK_D8B2E19BD823E37A FOREIGN KEY (section_id) REFERENCES form_section (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE form_section ADD CONSTRAINT FK_1C1F1AD46C2A0A71 FOREIGN KEY (enseigne_id) REFERENCES enseigne (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE enseigne ADD pole VARCHAR(255) NOT NULL, ADD ask_for_sepa TINYINT(1) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `option` ADD no_price_displayed TINYINT(1) NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE form_field DROP FOREIGN KEY FK_D8B2E19BD823E37A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE form_section DROP FOREIGN KEY FK_1C1F1AD46C2A0A71
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE form_field
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE form_section
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE enseigne DROP pole, DROP ask_for_sepa
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `option` DROP no_price_displayed
        SQL);
    }
}
