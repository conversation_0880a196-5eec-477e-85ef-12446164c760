<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250415084025 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE enseigne CHANGE name name VARCHAR(255) DEFAULT NULL, CHANGE logo logo VARCHAR(255) DEFAULT NULL, CHANGE contract contract VARCHAR(255) DEFAULT NULL, CHANGE identifier identifier VA<PERSON>HAR(10) DEFAULT NULL, CHANGE email_sender_email email_sender_email VARCHAR(255) DEFAULT NULL, CHANGE email_sender_name email_sender_name VA<PERSON>HAR(255) DEFAULT NULL, CHANGE email_aquitem_enabled email_aquitem_enabled TINYINT(1) DEFAULT NULL, CHANGE email_aquitem_attach_contract email_aquitem_attach_contract TINYINT(1) DEFAULT NULL, CHANGE email_enseigne_enabled email_enseigne_enabled TINYINT(1) DEFAULT NULL, CHANGE email_enseigne_attach_contract email_enseigne_attach_contract TINYINT(1) DEFAULT NULL, CHANGE email_subscriber_enabled email_subscriber_enabled TINYINT(1) DEFAULT NULL, CHANGE email_subscriber_attach_contract email_subscriber_attach_contract TINYINT(1) DEFAULT NULL, CHANGE contract_filename contract_filename VARCHAR(255) DEFAULT NULL, CHANGE link_mobility_accp link_mobility_accp VARCHAR(255) DEFAULT NULL, CHANGE pole pole VARCHAR(255) DEFAULT NULL, CHANGE ask_for_sepa ask_for_sepa TINYINT(1) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE enseigne CHANGE name name VARCHAR(255) NOT NULL, CHANGE logo logo VARCHAR(255) NOT NULL, CHANGE contract contract VARCHAR(255) NOT NULL, CHANGE identifier identifier VARCHAR(10) NOT NULL, CHANGE pole pole VARCHAR(255) NOT NULL, CHANGE ask_for_sepa ask_for_sepa TINYINT(1) NOT NULL, CHANGE email_sender_email email_sender_email VARCHAR(255) NOT NULL, CHANGE email_sender_name email_sender_name VARCHAR(255) NOT NULL, CHANGE email_aquitem_enabled email_aquitem_enabled TINYINT(1) NOT NULL, CHANGE email_aquitem_attach_contract email_aquitem_attach_contract TINYINT(1) NOT NULL, CHANGE email_enseigne_enabled email_enseigne_enabled TINYINT(1) NOT NULL, CHANGE email_enseigne_attach_contract email_enseigne_attach_contract TINYINT(1) NOT NULL, CHANGE email_subscriber_enabled email_subscriber_enabled TINYINT(1) NOT NULL, CHANGE email_subscriber_attach_contract email_subscriber_attach_contract TINYINT(1) NOT NULL, CHANGE contract_filename contract_filename VARCHAR(255) NOT NULL, CHANGE link_mobility_accp link_mobility_accp VARCHAR(255) NOT NULL
        SQL);
    }
}
