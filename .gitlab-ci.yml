image: gitlab.alienor.net:5050/dev-projets-aquitem/demat-adhesion:dev

default:
  tags:
    - anetdev

cache:
  - key:
      files:
        - composer.lock
      prefix: "$CI_COMMIT_REF_SLUG"
    paths:
      - vendor/
  - key:
      files:
        - yarn.lock
      prefix: "$CI_COMMIT_REF_SLUG"
    paths:
      - node_modules/
      - .yarn/


stages:
  - build
  - test
  - build-image
  - analyze
  - sonarqube-check
  - sonarqube-vulnerability-report
  - deploy

build:
  stage: build
  script:
    - composer install
  artifacts:
    paths:
      - "vendor"
      - "assets/vendor"
      - "public/assets"
    expire_in: 10 minutes

php-cs-fixer:
  stage: test
  script: PHP_CS_FIXER_IGNORE_ENV=1 ./vendor/bin/php-cs-fixer fix --dry-run --diff

security-checker:
  stage: analyze
  image: jakzal/phpqa:php8.4
  script:
    - local-php-security-checker  --path=./composer.lock --format=junit > local-php-security-checker.xml
  allow_failure: true
  artifacts:
    when: always
    paths:
      - local-php-security-checker.xml
    reports:
      junit: local-php-security-checker.xml
    expire_in: 30 minutes

phpunit:
  stage: test
  variables:
    SYMFONY_DEPRECATIONS_HELPER: disabled
    XDEBUG_MODE: coverage
    DATABASE_URL: 'mysql://root:root@database:3306/anetdb?serverVersion=10.4.7-MariaDB'
  services:
    - name: mariadb:10.4.7
      alias: database
      variables:
        MYSQL_ROOT_PASSWORD: root
        MYSQL_DATABASE: anetdb
        MYSQL_USER: userdb
        MYSQL_PASSWORD: passdb
  script:
    - yarn install
    - yarn encore production
    - php bin/console doctrine:database:create --env=test
    - php bin/console doctrine:migration:migrate --no-interaction --env=test
    - ./vendor/bin/phpunit --coverage-clover=coverage.xml  --log-junit report.xml
  artifacts:
    when: always
    paths:
      - coverage.xml
      - var/browser/source
      - var/log
    reports:
      junit: report.xml
    expire_in: 30 minutes

build-image-preprod:
  stage: build-image
  image: docker:latest
  needs: ["phpunit"]
  variables:
    IMAGE_TAG: preprod
    DOCKER_TLS_CERTDIR: "/certs"
    PDFJS_LICENSE_KEY: gb0J64pdNyPWpRDk0XsL
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build --build-arg PDFJS_LICENSE_KEY=$PDFJS_LICENSE_KEY php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - preprod

build-image-prod:
  stage: build-image
  image: docker:latest
  needs: ["phpunit"]
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    PDFJS_LICENSE_KEY: dJ9BE61LpUSFD93WZbY2
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build --build-arg PDFJS_LICENSE_KEY=$PDFJS_LICENSE_KEY php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - main

deploy-preprod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ["build-image-preprod"]
  when: manual
  environment:
    name: preprod
    url: https://adhesion-pharma.spp.aquitem.fr/
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update aquitem-preprod web-4462_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/demat-adhesion:preprod
  only:
    - preprod

deploy-prod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ["build-image-prod"]
  when: manual
  environment:
    name: production
    url: https://adhesion-pharma.zefid.fr/
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update aquitem-prod web-4463_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/demat-adhesion:prod
  only:
    - main

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner -Dsonar.projectVersion="$CI_COMMIT_SHORT_SHA"
  allow_failure: true
  only:
    - master
    - develop

#playwright:
#  stage: Playwright
#  image: mcr.microsoft.com/playwright:v1.29.2-focal
#  environment: preprod
#  variables:
#    HTTP_USER: devadmin
#    HTTP_PASSWORD: $ANET_DEVADMIN_PASS
#  dependencies: []
#  script:
#    - echo $CI_ENVIRONMENT_URL
#    - npm install -D @playwright/test@1.29.2
#    - npx playwright test
#  artifacts:
#    when: always
#    paths:
#      - playwright-report/
#      - test-results/
#      - results.xml
#    reports:
#      junit: results.xml
#    expire_in: 30 minutes
#  only:
#    - develop