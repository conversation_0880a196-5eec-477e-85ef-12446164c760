{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.4", "ext-ctype": "*", "ext-iconv": "*", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.7", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.13", "friendsofphp/php-cs-fixer": "^3.75", "karser/karser-recaptcha3-bundle": "^0.2.1", "knplabs/knp-snappy-bundle": "^1.9", "mikehaertl/php-pdftk": "^0.12.1", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.13", "silvertipsoftware/wkhtmltopdf-amd64": "^0.12.5", "symfony/asset": "7.2.*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/fake-sms-notifier": "7.2.*", "symfony/flex": "^2", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/html-sanitizer": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/lock": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "7.2.*", "symfony/process": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/rate-limiter": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/uid": "7.2.*", "symfony/ux-turbo": "2.24.0", "symfony/validator": "7.2.*", "symfony/web-link": "7.2.*", "symfony/webpack-encore-bundle": "^2.2", "symfony/yaml": "7.2.*", "twig/cssinliner-extra": "^3.4", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0", "vincentlanglet/twig-cs-fixer": "^3.5"}, "config": {"allow-plugins": {"symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "php-cs-fixer": "php-cs-fixer --config=./php-cs-fixer.dist.php"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.4", "phpunit/phpunit": "^9.5", "rector/rector": "*", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.47", "symfony/phpunit-bridge": "^6.1", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*", "zenstruck/foundry": "^2.4"}}