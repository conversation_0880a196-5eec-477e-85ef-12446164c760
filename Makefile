SUDO=sudo -u www-data
CONSOLE=php bin/console

install: composer cache-clear db-update build

update: cache-clear db-update build dump-env

### COMPOSER ###
composer:
	$(SUDO) composer install

### CONSOLE ###
cache-clear:
	$(SUDO) $(CONSOLE) cache:clear
	$(SUDO) $(CONSOLE) cache:warmup

db-list:
	$(SUDO) $(CONSOLE) doctrine:migration:list

db-update:
	$(SUDO) $(CONSOLE) doctrine:migration:migrate

build:
	$(SUDO) yarn install --force
	$(SUDO) yarn encore production

dump-env:
	$(SUDO) composer dump-env
	$(SUDO) $(CONSOLE) cache:clear