<?php

namespace App\Entity;

use App\DTO\FieldDefinition;
use App\Service\FormDefinitions;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\String\Slugger\AsciiSlugger;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
class FormField
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $label = null;

    #[ORM\Column(length: 255)]
    #[Assert\Choice(callback: [FormDefinitions::class, 'getFieldTypes'])]
    private ?string $type = null;

    #[ORM\Column]
    private bool $required = false;

    #[ORM\Column(type: 'json', nullable: true)]
    private array $options = [];

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $help = null;

    #[ORM\ManyToOne(inversedBy: 'fields')]
    #[ORM\JoinColumn(nullable: false)]
    private ?FormSection $section = null;

    #[ORM\Column]
    private int $position = 0;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $validationRules = null;

    #[ORM\Embedded(class: PdfDisplayConfig::class)]
    private PdfDisplayConfig $pdfDisplayConfig;

    public function __construct()
    {
        $this->pdfDisplayConfig = new PdfDisplayConfig();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function isRequired(): bool
    {
        return $this->required;
    }

    public function setRequired(bool $required): self
    {
        $this->required = $required;

        return $this;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;

        return $this;
    }

    public function getHelp(): ?string
    {
        return $this->help;
    }

    public function setHelp(?string $help): self
    {
        $this->help = $help;

        return $this;
    }

    public function getSection(): ?FormSection
    {
        return $this->section;
    }

    public function setSection(?FormSection $section): self
    {
        $this->section = $section;

        return $this;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getValidationRules(): ?string
    {
        return $this->validationRules;
    }

    public function setValidationRules(?string $validationRules): self
    {
        $this->validationRules = $validationRules;

        return $this;
    }

    public function getPdfDisplayConfig(): PdfDisplayConfig
    {
        return $this->pdfDisplayConfig;
    }

    public function setPdfDisplayConfig(PdfDisplayConfig $pdfDisplayConfig): self
    {
        $this->pdfDisplayConfig = $pdfDisplayConfig;

        return $this;
    }

    public function computeName(): string
    {
        return strtolower(new AsciiSlugger('fr')->slug(sprintf('%s-%s', $this->section->getName(), $this->label)));
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function setName(): self
    {
        $this->name = $this->computeName();

        return $this;
    }

    public function getDefinition(): FieldDefinition
    {
        return FormDefinitions::getFieldDefinition($this->type);
    }
}
