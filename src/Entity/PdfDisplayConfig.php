<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
class PdfDisplayConfig
{
    public const SECTION_SUMMARY = 'summary';
    public const SECTION_SIGNATURE = 'signature';

    public function __construct(
        #[ORM\Column]
        public bool $summary = false,
        #[ORM\Column]
        public bool $signature = false,
    ) {
    }

    public function getBySectionName(string $sectionName): bool
    {
        return $this->$sectionName ?? false;
    }
}
