<?php

namespace App\Entity;

use App\Model\AdhesionModel;
use App\Repository\EnseigneRepository;
use App\Service\CustomTemplater;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: EnseigneRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Enseigne
{
    private const string EXPR_EMAIL_AQUITEM_ENABLED = 'this.isEmailAquitemEnabled()';
    private const string EXPR_EMAIL_ENSEIGNE_ENABLED = 'this.isEmailEnseigneEnabled()';
    private const string EXPR_EMAIL_SUBSCRIBER_ENABLED = 'this.isEmailSubscriberEnabled()';

    public const string POLE_MAISON = 'maison';
    public const string POLE_SANTE = 'sante';
    public const string POLE_BEAUTE = 'beaute';

    public const array POLES = [
        'Pôle Maison' => self::POLE_MAISON,
        'Pole Santé' => self::POLE_SANTE,
        'Pole Beauté' => self::POLE_BEAUTE,
    ];

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $logo = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $contract = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $identifier = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Choice(choices: self::POLES)]
    private ?string $pole = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $textPresentation = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $textUnderLGO = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $textBeforeSignature = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $textAfterSignature = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $textContact = null;

    #[ORM\Column(length: 7, nullable: true)]
    private ?string $color1 = null;

    #[ORM\Column(length: 7, nullable: true)]
    private ?string $color2 = null;

    #[ORM\Column(length: 7, nullable: true)]
    private ?string $color3 = null;

    #[ORM\Column(nullable: true)]
    private ?bool $askForSepa = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $emailSenderEmail = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $emailSenderName = null;

    #[ORM\Column(nullable: true)]
    private ?bool $emailAquitemEnabled = null;

    #[ORM\Column(nullable: true)]
    private ?bool $emailAquitemAttachContract = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_AQUITEM_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailAquitemRecipientEmail = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_AQUITEM_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailAquitemSubject = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_AQUITEM_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailAquitemContent = null;

    #[ORM\Column(nullable: true)]
    private ?bool $emailEnseigneEnabled = null;

    #[ORM\Column(nullable: true)]
    private ?bool $emailEnseigneAttachContract = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_ENSEIGNE_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailEnseigneRecipientEmail = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_ENSEIGNE_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailEnseigneSubject = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_ENSEIGNE_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailEnseigneContent = null;

    #[ORM\Column(nullable: true)]
    private ?bool $emailSubscriberEnabled = null;

    #[ORM\Column(nullable: true)]
    private ?bool $emailSubscriberAttachContract = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_SUBSCRIBER_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailSubscriberSubject = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\When(
        expression: self::EXPR_EMAIL_SUBSCRIBER_ENABLED,
        constraints: [
            new Assert\Required(),
            new Assert\NotBlank(),
        ],
    )]
    private ?string $emailSubscriberContent = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $contractFilename = null;

    #[ORM\Column(nullable: true)]
    private ?bool $formCompleted = false;

    /**
     * @var Collection<int, Option>
     */
    #[ORM\OneToMany(mappedBy: 'enseigne', targetEntity: Option::class, cascade: ['persist'], orphanRemoval: true)]
    #[ORM\OrderBy(['position' => 'ASC'])]
    private Collection $options;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $linkMobilityAccp = null;

    /**
     * @var Collection<int, FormSection>
     */
    #[ORM\OneToMany(mappedBy: 'enseigne', targetEntity: FormSection::class, cascade: ['persist'], orphanRemoval: true)]
    #[ORM\OrderBy(['position' => 'ASC'])]
    private Collection $sections;

    public function __construct()
    {
        $this->options = new ArrayCollection();
        $this->sections = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(string $logo): self
    {
        $this->logo = $logo;

        return $this;
    }

    public function getContract(): ?string
    {
        return $this->contract;
    }

    public function setContract(string $contract): self
    {
        $this->contract = $contract;

        return $this;
    }

    public function getIdentifier(): ?string
    {
        return $this->identifier;
    }

    public function setIdentifier(string $identifier): self
    {
        $this->identifier = $identifier;

        return $this;
    }

    public function getPole(): ?string
    {
        return $this->pole;
    }

    public function setPole(?string $pole): self
    {
        $this->pole = $pole;

        return $this;
    }

    public function getTextPresentation(): ?string
    {
        return $this->textPresentation;
    }

    public function setTextPresentation(?string $textPresentation): self
    {
        $this->textPresentation = $textPresentation;

        return $this;
    }

    public function getTextUnderLGO(): ?string
    {
        return $this->textUnderLGO;
    }

    public function setTextUnderLGO(?string $textUnderLGO): self
    {
        $this->textUnderLGO = $textUnderLGO;

        return $this;
    }

    public function getTextBeforeSignature(): ?string
    {
        return $this->textBeforeSignature;
    }

    public function setTextBeforeSignature(?string $textBeforeSignature): self
    {
        $this->textBeforeSignature = $textBeforeSignature;

        return $this;
    }

    public function getTextAfterSignature(): ?string
    {
        return $this->textAfterSignature;
    }

    public function setTextAfterSignature(?string $textAfterSignature): self
    {
        $this->textAfterSignature = $textAfterSignature;

        return $this;
    }

    public function getTextContact(): ?string
    {
        return $this->textContact;
    }

    public function setTextContact(?string $textContact): self
    {
        $this->textContact = $textContact;

        return $this;
    }

    public function getColor1(): ?string
    {
        return $this->color1;
    }

    public function setColor1(?string $color1): self
    {
        $this->color1 = $color1;

        return $this;
    }

    public function getColor2(): ?string
    {
        return $this->color2;
    }

    public function setColor2(?string $color2): self
    {
        $this->color2 = $color2;

        return $this;
    }

    public function getColor3(): ?string
    {
        return $this->color3;
    }

    public function setColor3(?string $color3): self
    {
        $this->color3 = $color3;

        return $this;
    }

    public function getEmailSenderEmail(): ?string
    {
        return $this->emailSenderEmail;
    }

    public function getAskForSepa(): ?bool
    {
        return $this->askForSepa;
    }

    public function setAskForSepa(?bool $askForSepa): self
    {
        $this->askForSepa = $askForSepa;

        return $this;
    }

    public function setEmailSenderEmail(string $emailSenderEmail): self
    {
        $this->emailSenderEmail = $emailSenderEmail;

        return $this;
    }

    public function getEmailSenderName(): ?string
    {
        return $this->emailSenderName;
    }

    public function setEmailSenderName(string $emailSenderName): self
    {
        $this->emailSenderName = $emailSenderName;

        return $this;
    }

    public function isEmailAquitemEnabled(): ?bool
    {
        return $this->emailAquitemEnabled;
    }

    public function setEmailAquitemEnabled(bool $emailAquitemEnabled): self
    {
        $this->emailAquitemEnabled = $emailAquitemEnabled;

        return $this;
    }

    public function isEmailAquitemAttachContract(): ?bool
    {
        return $this->emailAquitemAttachContract;
    }

    public function setEmailAquitemAttachContract(bool $emailAquitemAttachContract): self
    {
        $this->emailAquitemAttachContract = $emailAquitemAttachContract;

        return $this;
    }

    public function getEmailAquitemRecipientEmail(): ?string
    {
        return $this->emailAquitemRecipientEmail;
    }

    public function setEmailAquitemRecipientEmail(?string $emailAquitemRecipientEmail): self
    {
        $this->emailAquitemRecipientEmail = $emailAquitemRecipientEmail;

        return $this;
    }

    public function getEmailAquitemSubject(): ?string
    {
        return $this->emailAquitemSubject;
    }

    public function setEmailAquitemSubject(?string $emailAquitemSubject): self
    {
        $this->emailAquitemSubject = $emailAquitemSubject;

        return $this;
    }

    public function getEmailAquitemContent(): ?string
    {
        return $this->emailAquitemContent;
    }

    public function setEmailAquitemContent(?string $emailAquitemContent): self
    {
        $this->emailAquitemContent = $emailAquitemContent;

        return $this;
    }

    public function isEmailEnseigneEnabled(): ?bool
    {
        return $this->emailEnseigneEnabled;
    }

    public function setEmailEnseigneEnabled(bool $emailEnseigneEnabled): self
    {
        $this->emailEnseigneEnabled = $emailEnseigneEnabled;

        return $this;
    }

    public function isEmailEnseigneAttachContract(): ?bool
    {
        return $this->emailEnseigneAttachContract;
    }

    public function setEmailEnseigneAttachContract(bool $emailEnseigneAttachContract): self
    {
        $this->emailEnseigneAttachContract = $emailEnseigneAttachContract;

        return $this;
    }

    public function getEmailEnseigneRecipientEmail(): ?string
    {
        return $this->emailEnseigneRecipientEmail;
    }

    public function setEmailEnseigneRecipientEmail(?string $emailEnseigneRecipientEmail): self
    {
        $this->emailEnseigneRecipientEmail = $emailEnseigneRecipientEmail;

        return $this;
    }

    public function getEmailEnseigneSubject(): ?string
    {
        return $this->emailEnseigneSubject;
    }

    public function setEmailEnseigneSubject(?string $emailEnseigneSubject): self
    {
        $this->emailEnseigneSubject = $emailEnseigneSubject;

        return $this;
    }

    public function getEmailEnseigneContent(): ?string
    {
        return $this->emailEnseigneContent;
    }

    public function setEmailEnseigneContent(?string $emailEnseigneContent): self
    {
        $this->emailEnseigneContent = $emailEnseigneContent;

        return $this;
    }

    public function isEmailSubscriberEnabled(): ?bool
    {
        return $this->emailSubscriberEnabled;
    }

    public function setEmailSubscriberEnabled(bool $emailSubscriberEnabled): self
    {
        $this->emailSubscriberEnabled = $emailSubscriberEnabled;

        return $this;
    }

    public function isEmailSubscriberAttachContract(): ?bool
    {
        return $this->emailSubscriberAttachContract;
    }

    public function setEmailSubscriberAttachContract(bool $emailSubscriberAttachContract): self
    {
        $this->emailSubscriberAttachContract = $emailSubscriberAttachContract;

        return $this;
    }

    public function getEmailSubscriberSubject(): ?string
    {
        return $this->emailSubscriberSubject;
    }

    public function setEmailSubscriberSubject(?string $emailSubscriberSubject): self
    {
        $this->emailSubscriberSubject = $emailSubscriberSubject;

        return $this;
    }

    public function getEmailSubscriberContent(): ?string
    {
        return $this->emailSubscriberContent;
    }

    public function setEmailSubscriberContent(?string $emailSubscriberContent): self
    {
        $this->emailSubscriberContent = $emailSubscriberContent;

        return $this;
    }

    public function getContractFilename(): ?string
    {
        return $this->contractFilename;
    }

    public function setContractFilename(string $contractFilename): self
    {
        $this->contractFilename = $contractFilename;

        return $this;
    }

    public function isFormCompleted(): ?bool
    {
        return $this->formCompleted;
    }

    public function setFormCompleted(bool $formCompleted): self
    {
        $this->formCompleted = $formCompleted;

        return $this;
    }

    /**
     * @return Collection<int, Option>
     */
    public function getOptions(): Collection
    {
        return $this->options;
    }

    public function addOption(Option $option): self
    {
        if (!$this->options->contains($option)) {
            $this->options->add($option);
            $option->setEnseigne($this);
        }

        return $this;
    }

    public function removeOption(Option $option): self
    {
        // set the owning side to null (unless already changed)
        if ($this->options->removeElement($option) && $option->getEnseigne() === $this) {
            $option->setEnseigne(null);
        }

        return $this;
    }

    /**
     * @return Collection<int, FormSection>
     */
    public function getSections(): Collection
    {
        return $this->sections;
    }

    public function addSection(FormSection $section): self
    {
        if (!$this->sections->contains($section)) {
            $this->sections->add($section);
            $section->setEnseigne($this);
        }

        return $this;
    }

    public function removeSection(FormSection $section): self
    {
        // set the owning side to null (unless already changed)
        if ($this->sections->removeElement($section) && $section->getEnseigne() === $this) {
            $section->setEnseigne(null);
        }

        return $this;
    }

    public function getColor(int $index, $format = 'hex'): ?string
    {
        $color = match ($index) {
            1 => $this->getColor1(),
            2 => $this->getColor2(),
            3 => $this->getColor3(),
        };
        if ('rgb' === $format && $color) {
            return sprintf('%s, %s, %s', ...sscanf($color, '#%02x%02x%02x'));
        }

        return $color;
    }

    public function getContractName(AdhesionModel $adhesion)
    {
        return new CustomTemplater()->render($this->getContractFilename(), $this, $adhesion);
    }

    public function getLinkMobilityAccp(): ?string
    {
        return $this->linkMobilityAccp;
    }

    public function setLinkMobilityAccp(string $linkMobilityAccp): self
    {
        $this->linkMobilityAccp = $linkMobilityAccp;

        return $this;
    }

    public function getAvailableTags(): array
    {
        return array_merge(...$this->sections->map(function (FormSection $section) {
            return $section->getFields()->map(function (FormField $field) {
                return '{' . $field->getName() . '}';
            })->toArray();
        })->toArray());
    }

    /**
     * @return array<FormField>
     */
    public function getFieldsForPdfSection(string $sectionName): array
    {
        $fields = [];

        foreach ($this->getAllFields() as $field) {
            if ($field->getPdfDisplayConfig()->getBySectionName($sectionName)) {
                $fields[] = $field;
            }
        }

        return $fields;
    }

    /**
     * @return array<FormField>
     */
    public function getAllFields(): array
    {
        $fields = [];
        foreach ($this->getSections() as $section) {
            foreach ($section->getFields() as $field) {
                $fields[] = $field;
            }
        }

        return $fields;
    }

    /**
     * @return array<FormField>
     */
    public function getAllRequiredFields(): array
    {
        return $this->sections->map(function (FormSection $section) {
            return $section->getFields()->filter(function (FormField $field) {
                return $field->getDefinition()->required;
            })->toArray();
        })->toArray();
    }

    public function getFieldByType(string $fieldType): FormField
    {
        foreach ($this->getAllFields() as $field) {
            if ($field->getType() === $fieldType) {
                return $field;
            }
        }

        throw new \InvalidArgumentException(sprintf('Le champ de type %s n\'existe pas', $fieldType));
    }
}
