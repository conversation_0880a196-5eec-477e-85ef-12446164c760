<?php

namespace App\Form;

use App\Entity\Enseigne;
use App\Entity\PdfDisplayConfig;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PdfDisplayConfigType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Enseigne $enseigne */
        $enseigne = $options['enseigne'];

        $builder
            ->add('summary', CheckboxType::class, [
                'label' => 'Sommaire',
                'label_attr' => [
                    'class' => 'checkbox-inline',
                ],
            ])
            ->add('signature', CheckboxType::class, [
                'label' => 'Signature',
                'label_attr' => [
                    'class' => 'checkbox-inline',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'enseigne' => null,
            'data_class' => PdfDisplayConfig::class,
        ]);
    }
}
