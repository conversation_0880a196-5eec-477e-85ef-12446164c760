<?php

namespace App\Form;

use App\Entity\Enseigne;
use App\Model\AdhesionSepaFormModel;
use App\Service\FormDefinitions;
use App\Service\TestManager;
use Karser\Recaptcha3Bundle\Form\Recaptcha3Type;
use Karser\Recaptcha3Bundle\Validator\Constraints\Recaptcha3;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CountryType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Bic;
use Symfony\Component\Validator\Constraints\Country;
use Symfony\Component\Validator\Constraints\Iban;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;

class AdhesionSepaType extends AbstractType
{
    public function __construct(
        protected TestManager $testManager,
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Enseigne $enseigne */
        $enseigne = $options['enseigne'];

        if ($enseigne->getAskForSepa()) {
            $builder
                ->add('iban', null, [
                    'required' => true,
                    'label' => 'IBAN',
                    'attr' => ['placeholder' => '____ ____ ____ ____ ____ ____ ___'],
                    'constraints' => [
                        new Iban(),
                        new NotBlank(),
                    ],
                ])
                ->add('bic', null, [
                    'required' => true,
                    'label' => 'BIC',
                    'constraints' => [
                        // La contrainte BIC se trouve dans le DTO car ibanPropertyPath ne peut pas se fier au form
                        new NotBlank(),
                    ],
                ]);

            $builder->get('iban')
                ->addModelTransformer(new CallbackTransformer(
                    function ($ibanValue) {
                        return $ibanValue;
                    },
                    // On supprime les espaces dans l'iban
                    function ($ibanString) {
                        return str_replace(' ', '', $ibanString);
                    }
                ));

            $builder
                ->add('sepaName', null, [
                    'required' => true,
                    'label' => 'Nom / Prénoms du débiteur',
                    'help' => '(26 caractères maximum, espace compris)',
                    'constraints' => [
                        new Length(
                            max: 26,
                            maxMessage: 'Le libellé ne peut pas dépasser 26 caractères, espaces compris',
                        ),
                    ],
                ])
                ->add('address', null, [
                    'required' => true,
                    'label' => 'Adresse',
                ])
                ->add('address2', null, [
                    'label' => 'Complément d\'adresse',
                ])
                ->add('zipCode', null, [
                    'required' => true,
                    'label' => 'Code postal',
                    'constraints' => [
                        new Length(max: 12),
                    ],
                ])
                ->add('city', null, [
                    'required' => true,
                    'label' => 'Ville',
                ])
                ->add('country', CountryType::class, [
                    'required' => true,
                    'label' => 'Pays',
                    'disabled' => true,
                    'constraints' => [
                        new Country(),
                    ],
                ])
            ;
        }

        $builder->add('signaturePhone', TelType::class, [
            'required' => true,
            'label' => 'Tél. portable',
            'help' => 'Pour signer électroniquement ce contrat, vous devez avoir accès à ce téléphone afin de recevoir un code de confirmation',
            'attr' => [
                'placeholder' => '+33',
                'data-controller' => 'inputmask',
                'data-inputmask-pattern-value' => FormDefinitions::PHONE_MASK,
            ],
            'constraints' => [
                new NotBlank(),
                new Regex(
                    pattern: FormDefinitions::PATTERN_PHONE,
                    message: FormDefinitions::MESSAGE_PHONE,
                    htmlPattern: false,
                    match: true,
                ),
            ],
        ]);

        /**
         * Désactivation de la vérification du recaptcha selon la présence d'un cookie,
         * pour permettre l'execution de tests automatisés.
         */
        $recaptchaConstraint = new Recaptcha3();
        if ($this->testManager->isTesting()) {
            $recaptchaConstraint = [];
        }

        $builder->add('captcha', Recaptcha3Type::class, [
            'constraints' => $recaptchaConstraint,
            'action_name' => 'sepa_form',
            'locale' => 'fr',
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'enseigne' => null,
            'data_class' => AdhesionSepaFormModel::class,
        ]);
    }
}
