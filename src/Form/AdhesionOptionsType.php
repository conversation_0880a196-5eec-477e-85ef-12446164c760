<?php

namespace App\Form;

use App\Entity\Enseigne;
use App\Model\AdhesionModel;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AdhesionOptionsType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Enseigne $enseigne */
        $enseigne = $options['enseigne'];

        $builder->add('options', CollectionType::class, [
            'entry_type' => AdhesionOptionType::class,
            'entry_options' => ['label' => false],
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'enseigne' => null,
            'data_class' => AdhesionModel::class,
        ]);
    }
}
