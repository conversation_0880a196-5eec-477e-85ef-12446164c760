<?php

namespace App\Form;

use App\Entity\Option;
use App\Model\AdhesionOptionsFormModel;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Range;

class AdhesionOptionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->addEventListener(
                FormEvents::PRE_SET_DATA,
                function (FormEvent $event) {
                    /** @var Option $option */
                    $option = $event->getData()->option;

                    $config = [
                        'label' => false,
                        'constraints' => [
                            new Range([
                                'min' => 0,
                                'max' => 99,
                            ]),
                        ],
                    ];

                    if (!$option->isHasQuantity()) {
                        $config['disabled'] = true;
                        $config['data'] = 1;
                    }

                    if ($option->isHasQuantity() && !$option->isIsFree()) {
                        $config['attr'] = [
                            'data-price' => $option->getUnitPrice(),
                            'data-id' => $option->getId(),
                        ];
                    }

                    $event->getForm()->add('quantity', IntegerType::class, $config);
                }
            );
    }

    public function onPreSetData(FormEvent $event): void
    {
        dd($event);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => AdhesionOptionsFormModel::class,
        ]);
    }
}
