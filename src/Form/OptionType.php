<?php

namespace App\Form;

use App\Entity\Option;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OptionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', null, [
                'label' => "Nom de l'option",
                'required' => true,
                'attr' => [
                    'placeholder' => "Nom de l'option",
                ],
                'row_attr' => [
                    'class' => 'mb-3 form-floating',
                ],
            ])
            ->add('text', null, [
                'label' => false,
                'attr' => ['rows' => 6],
            ])
            ->add('hasQuantity', null, [
                'label' => 'Champ quantité',
                'row_attr' => [
                    'class' => 'col-auto',
                ],
            ])
            ->add('isFree', null, [
                'label' => 'Offert',
                'row_attr' => [
                    'class' => 'col-auto',
                ],
            ])
            ->add('noPriceDisplayed', null, [
                'label' => 'Aucun prix',
                'row_attr' => [
                    'class' => 'col-auto',
                ],
            ])
            ->add('unitPrice', NumberType::class, [
                'label' => 'Prix unitaire',
                'required' => false,
                'html5' => true,
                'attr' => [
                    'placeholder' => 'Prix unitaire',
                ],
                'row_attr' => [
                    'class' => 'col-auto form-floating',
                ],
            ])
            ->add('position', HiddenType::class, [
                'label' => 'Position',
                'required' => true,
                'attr' => ['class' => 'position'],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Option::class,
        ]);
    }
}
