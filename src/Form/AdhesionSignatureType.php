<?php

namespace App\Form;

use App\Entity\Enseigne;
use App\Model\AdhesionSignatureFormModel;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\IsTrue;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;

class AdhesionSignatureType extends AbstractType
{
    public const string TEXT_CGV = 'Je reconnais avoir pris connaissance des conditions tarifaires et des conditions générales et particulières de vente énoncées ci avant';
    public const string TEXT_SEPA = "J'autorise AQUITEM à envoyer des instructions à ma banque pour débiter mon compte (Mandant de prélèvement SEPA)";
    public const string TEXT_DEBIT = "J'accepte le règlement par prélèvement à 30 jours date de facture";

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Enseigne $enseigne */
        $enseigne = $options['enseigne'];

        if ($enseigne->getAskForSepa()) {
            $builder
                ->add('sepaAccepted', CheckboxType::class, [
                    'required' => true,
                    'label' => self::TEXT_SEPA,
                    'constraints' => [
                        new IsTrue(),
                    ],
                ])
                ->add('debitAccepted', CheckboxType::class, [
                    'required' => true,
                    'label' => self::TEXT_DEBIT,
                    'constraints' => [
                        new IsTrue(),
                    ],
                ]);
        }

        $builder
            ->add('cgvAccepted', CheckboxType::class, [
                'required' => true,
                'label' => self::TEXT_CGV,
                'constraints' => [
                    new IsTrue(),
                ],
            ])
            ->add('signatureData', HiddenType::class, [
                'required' => true,
                'error_bubbling' => false,
                'label' => 'Votre signature',
                'constraints' => [
                    new NotBlank(message: 'La signature est obligatoire'),
                    new Regex(
                        pattern: '/data:image\/png;base64.*/',
                        htmlPattern: false,
                        match: true,
                    ),
                ],
            ])
            ->add('pin', null, [
                'required' => true,
                'label' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'enseigne' => null,
            'data_class' => AdhesionSignatureFormModel::class,
        ]);
    }
}
