<?php

namespace App\Form\Enseigne;

use App\Entity\Enseigne;
use App\Service\CustomTemplater;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class EnseigneNotificationsType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Enseigne $enseigne */
        $enseigne = $builder->getData();

        $getTagConstraints = function (array $exclusions = []) use ($enseigne) {
            return new Callback([
                'callback' => $this->validateTags(...),
            ], payload: ['enseigne' => $enseigne, 'exclusions' => $exclusions]);
        };

        $helpVars = $this->getHelpVars($enseigne);

        $emailEnabled = [
            'label' => 'Activé',
            'row_attr' => ['class' => 'checkbox-inline me-5'],
        ];
        $emailContract = [
            'label' => 'Joindre le contrat en pièce jointe',
            'row_attr' => ['class' => 'checkbox-inline'],
        ];
        $emailRecipients = [
            'label' => 'Mail(s) de destination',
        ];
        $emailSubject = [
            'label' => 'Sujet du mail',
            'help' => $helpVars,
            'constraints' => [
                $getTagConstraints(),
            ],
        ];
        $emailContent = [
            'label' => 'Texte du mail',
            'attr' => ['rows' => 6],
            'help' => $helpVars,
            'constraints' => [
                $getTagConstraints(),
            ],
        ];

        $builder
            ->add('contractFilename', null, [
                'label' => 'Nom du fichier de contrat',
                'help' => $this->getHelpVars($enseigne, ['{numero-contrat}']),
                'constraints' => [
                    $getTagConstraints(['{numero-contrat}']),
                ],
            ])
            ->add('emailSenderEmail', EmailType::class, [
                'label' => 'Adresse e-mail expéditeur',
                'required' => true,
                'constraints' => [
                    new Email(),
                ],
            ])
            ->add('emailSenderName', null, [
                'label' => "Nom de l'expéditeur des mails",
                'required' => true,
            ])
            // Email Aquitem
            ->add('emailAquitemEnabled', null, $emailEnabled)
            ->add('emailAquitemAttachContract', null, $emailContract)
            ->add('emailAquitemRecipientEmail', null, $emailRecipients)
            ->add('emailAquitemSubject', null, $emailSubject)
            ->add('emailAquitemContent', null, $emailContent)
            // Email Enseigne
            ->add('emailEnseigneEnabled', null, $emailEnabled)
            ->add('emailEnseigneAttachContract', null, $emailContract)
            ->add('emailEnseigneRecipientEmail', null, $emailRecipients)
            ->add('emailEnseigneSubject', null, $emailSubject)
            ->add('emailEnseigneContent', null, $emailContent)
            // Email Adhérent
            ->add('emailSubscriberEnabled', null, $emailEnabled)
            ->add('emailSubscriberAttachContract', null, $emailContract)
            ->add('emailSubscriberSubject', null, $emailSubject)
            ->add('emailSubscriberContent', null, $emailContent);
    }

    public static function validateTags(?string $value, ExecutionContextInterface $context, ?array $payload = null)
    {
        if (!$value) {
            return;
        }

        $enseigne = $payload['enseigne'];
        $exclusions = $payload['exclusions'] ?? [];

        preg_match_all('/\{(.*?)\}/', $value, $matches);

        if ($matches[0]) {
            foreach ($matches[0] as $tag) {
                if (!in_array($tag, CustomTemplater::getAvailableTags($enseigne, $exclusions))) {
                    $context
                        ->buildViolation(sprintf("La variable %s n'existe pas", $tag))
                        ->atPath(str_replace($context->getPropertyPath(), '.data', ''))
                        ->addViolation();
                }
            }
        }
    }

    public function getHelpVars(?Enseigne $enseigne = null, array $exclusions = [])
    {
        return 'Variables possibles : ' . join(' ', CustomTemplater::getAvailableTags($enseigne, $exclusions));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Enseigne::class,
        ]);
    }
}
