<?php

namespace App\Form\Enseigne;

use App\Entity\Enseigne;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\Image;

class EnseigneIdentificationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', null, [
                'label' => "Nom de l'enseigne ou du programme",
                'required' => true,
            ])
            ->add('logoFile', FileType::class, [
                'label' => "Logo de l'enseigne (PNG / 5mo max / 250px x 250px)",
                'mapped' => false,
                'required' => false === $options['edit'],
                'constraints' => [
                    new File([
                        'maxSize' => '5120k',
                        'mimeTypes' => [
                            'image/png',
                        ],
                        'mimeTypesMessage' => 'Veuillez uploader un fichier PNG valide',
                    ]),
                    new Image([
                        'maxWidth' => 250,
                        'maxHeight' => 250,
                    ]),
                ],
            ])
            ->add('identifier', null, [
                'label' => "Code d'accès au formulaire d'adhésion",
                'required' => true,
            ])
            ->add('contractFile', FileType::class, [
                'label' => 'Contrat (PDF / 5mo max)',
                'mapped' => false,
                'required' => false === $options['edit'],
                'constraints' => [
                    new File([
                        'maxSize' => '5120k',
                        'mimeTypes' => [
                            'application/pdf',
                            'application/x-pdf',
                        ],
                        'mimeTypesMessage' => 'Veuillez uploader un fichier PDF valide',
                    ]),
                ],
            ])
            ->add('pole', ChoiceType::class, [
                'label' => 'Pôle de rattachement',
                'required' => true,
                'choices' => Enseigne::POLES,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'edit' => false,
            'data_class' => Enseigne::class,
        ]);
    }
}
