<?php

namespace App\Form\Enseigne;

use App\Entity\Enseigne;
use App\Entity\FormSection;
use App\Form\FormSectionType;
use App\Service\FormDefinitions;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class EnseigneFormulaireDynamiqueType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('sections', CollectionType::class, [
                'entry_type' => FormSectionType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'label' => 'Sections du formulaire',
                'entry_options' => [
                    'enseigne' => $builder->getData(),
                ],
                'constraints' => [
                    new Callback(
                        ['callback' => static function (Collection $data, ExecutionContextInterface $context) {
                            $mandatoryFields = FormDefinitions::getRequiredFields();

                            $formTypes = [];

                            /** @var FormSection $section */
                            foreach ($data as $section) {
                                foreach ($section->getFields() as $field) {
                                    $formTypes[$field->computeName()] = $field->getType();
                                }
                            }

                            // On vérifie que tous les champs obligatoires sont présents
                            foreach ($mandatoryFields as $type => $field) {
                                if (!in_array($type, $formTypes)) {
                                    $context
                                        ->buildViolation(sprintf('La présence du champ %s est obligatoire', $field->label))
                                        ->addViolation();
                                }
                            }
                        }]
                    ),
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Enseigne::class,
        ]);
    }
}
