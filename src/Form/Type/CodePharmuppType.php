<?php

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Regex;

class CodePharmuppType extends AbstractType
{
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'constraints' => [
                new Length(
                    exactly: 8,
                    exactMessage: 'Le code Business centrale PHARM-UPP contenir exactement 8 chiffres',
                ),
                new Regex(
                    pattern: '/(F)[A-Z0-9]{7}/',
                    message: 'Le code Business centrale PHARM-UPP est invalide',
                    htmlPattern: false,
                    match: true,
                ),
            ],
            'attr' => [
                'data-controller' => 'inputmask',
                'data-inputmask-pattern-value' => '\F*{7}',
            ],
        ]);
    }

    public function getParent(): string
    {
        return TextType::class;
    }
}
