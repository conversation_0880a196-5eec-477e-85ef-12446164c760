<?php

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Regex;

class FinessType extends AbstractType
{
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'constraints' => [
                new Length(
                    exactly: 9,
                    exactMessage: 'Le numéro FINESS doit contenir exactement 9 chiffres',
                ),
                new Regex(
                    pattern: '/\d{2}2\d{6}/',
                    message: 'Le numéro FINESS est invalide',
                    htmlPattern: false,
                    match: true
                ),
            ],
            'attr' => [
                'data-controller' => 'inputmask',
                'data-inputmask-pattern-value' => '9{9}',
            ],
        ]);
    }

    public function getParent(): string
    {
        return TextType::class;
    }
}
