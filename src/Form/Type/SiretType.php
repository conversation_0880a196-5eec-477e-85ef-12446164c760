<?php

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Regex;

class SiretType extends AbstractType
{
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'constraints' => [
                new Length(
                    exactly: 14,
                    exactMessage: 'Le numéro SIRET doit contenir exactement 14 chiffres',
                ),
                new Regex(
                    pattern: '/\d{14}/',
                    message: 'Veuillez respecter le format suivant : numéro composé de 14 chiffres.',
                    htmlPattern: false,
                    match: true
                ),
            ],
            'attr' => [
                'data-controller' => 'inputmask',
                'data-inputmask-pattern-value' => '9{14}',
            ],
        ]);
    }

    public function getParent(): string
    {
        return TextType::class;
    }
}
