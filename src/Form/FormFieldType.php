<?php

namespace App\Form;

use App\Entity\FormField;
use App\Entity\PdfDisplayConfig;
use App\Service\FormDefinitions;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Event\PostSetDataEvent;
use Symfony\Component\Form\Event\SubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FormFieldType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('label', TextType::class, [
                'label' => 'Libellé',
                'required' => true,
                'attr' => [
                    'placeholder' => 'Libellé',
                ],
                'row_attr' => [
                    'class' => 'form-floating mb-3',
                ],
            ])
            ->add('help', TextareaType::class, [
                'label' => 'Description du champ',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Description du champ',
                ],
                'row_attr' => [
                    'class' => 'form-floating mb-3',
                ],
            ])
            ->add('type', HiddenType::class, [
                'label' => 'Type de champ',
                'required' => true,
            ])
            ->add('required', null, [
                'label' => 'Champ obligatoire',
            ])
            ->add('position', HiddenType::class, [
                'label' => 'Position',
                'required' => true,
                'attr' => ['class' => 'position'],
            ])
            ->add('options', HiddenType::class, [
                'required' => false,
            ])
            ->add('pdfDisplayConfig', PdfDisplayConfigType::class, [
                'label' => 'Configuration de l\'affichage PDF',
                'required' => false,
                'label_attr' => [
                    'class' => 'checkbox-inline',
                ],
                'enseigne' => $options['enseigne'],
            ])
        ;

        /*
         * Sur les champs obligatoire on disable la checkbox required pour qu'elle ne puisse pas être décochée
         * (comportement répliqué en JS pour la création dynamique)
         */
        $builder->addEventListener(FormEvents::POST_SET_DATA, function (PostSetDataEvent $event): void {
            /** @var FormField $field */
            $field = $event->getData();
            $form = $event->getForm();

            if (!$field) {
                return;
            }

            if (in_array($field->getType(), array_keys(FormDefinitions::getRequiredFields()))) {
                $field->setRequired(true);
                $form->add('required', null, [
                    'label' => 'Champ obligatoire',
                    'data' => true,
                    'required' => true,
                    'disabled' => true,
                ]);
            }
        });

        /*
         * Force les champs obligatoire à avoir true comme valeur dans required car le disabled fait qu'ils ne sont pas envoyés
         */
        $builder->addEventListener(FormEvents::SUBMIT, function (SubmitEvent $event): void {
            /** @var FormField $field */
            $field = $event->getData();

            if (!$field) {
                return;
            }

            if ('managerEmail' === $field->getType()) {
                $field->setRequired(true);
                $event->setData($field);
            }
        });

        // Gestion du champ json
        $builder->get('options')
            ->addModelTransformer(new CallbackTransformer(
                function ($arrayValue): ?string {
                    // transform the array to a string
                    return is_array($arrayValue) ? json_encode($arrayValue) : $arrayValue;
                },
                function ($stringValue): ?array {
                    // transform the string back to an array
                    return (empty($stringValue) ? [] : json_decode($stringValue, true)) ?: [];
                }
            ))
        ;

        // Si on reçoit null dans pdfDisplayConfig on l'initialise
        $builder->get('pdfDisplayConfig')
            ->addModelTransformer(new CallbackTransformer(
                function ($savedValue): ?PdfDisplayConfig {
                    return $savedValue;
                },
                function ($formValue): ?PdfDisplayConfig {
                    return $formValue ?? new PdfDisplayConfig();
                }
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'enseigne' => null,
            'data_class' => FormField::class,
        ]);
    }
}
