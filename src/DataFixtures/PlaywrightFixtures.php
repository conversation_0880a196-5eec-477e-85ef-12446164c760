<?php

namespace App\DataFixtures;

use App\Entity\Enseigne;
use App\Entity\FormField;
use App\Entity\FormSection;
use App\Entity\Option;
use App\Story\DefaultStory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class PlaywrightFixtures extends Fixture implements FixtureGroupInterface
{
    public const TEST_IDENTIFIER = 'PLAYWRIGHT';

    public function __construct(
        #[Autowire(param: 'targetDirectory')] protected string $targetDirectory,
        protected EntityManagerInterface $entityManager,
    ) {
    }

    public function load(ObjectManager $manager): void
    {
        $this->clean();

        copy(sprintf('%s/files/sample-logo.png', __DIR__), sprintf('%s/sample-logo.png', $this->targetDirectory));
        copy(sprintf('%s/files/sample-contract.pdf', __DIR__), sprintf('%s/sample-contract.pdf', $this->targetDirectory));

        DefaultStory::createDemoEnseigne(self::TEST_IDENTIFIER);
    }

    public function clean(): void
    {
        $enseigne = $this->entityManager->getRepository(Enseigne::class)->findOneBy(['identifier' => self::TEST_IDENTIFIER]);
        if ($enseigne) {
            $this->entityManager->remove($enseigne);
            $this->entityManager->flush();
        }
    }

    public static function getSampleEnseigne()
    {
        $enseigne = new Enseigne();

        $option1 = (new Option())
            ->setName('Welcome Pack : 500 Cartes Privilège')
            ->setText('Offert à l’inscription')
            ->setHasQuantity(false)
            ->setIsFree(true)
            ->setPosition(1)
        ;

        $option2 = (new Option())
            ->setName('Pack supplémentaire de 500 Cartes Privilège – 135€HT hors frais d’expédition')
            ->setText('Les frais d’expédition seront facturés à la pharmacie. 
Les factures émises par AQUITEM seront réglées exclusivement par prélèvement à 30 jours date de facture. Elles seront envoyées à l’adresse e-mail des officines.')
            ->setHasQuantity(true)
            ->setUnitPrice(135)
            ->setIsFree(false)
            ->setPosition(2)
        ;

        $enseigne
            ->setName('PLAYWRIGHT TEST')
            ->setIdentifier(self::TEST_IDENTIFIER)
            ->setLogo('sample-logo.png')
            ->setContract('sample-contract.pdf')
            ->setContractFilename('UPP_{finess}_{date}')

            ->setTextPresentation('Pour adhérer au programme Cartes Privilège PHARM-UPP, suivez les 5 étapes de la procédure d’adhésion.
Elle doit s’effectuer en une seule fois et prend environ 10 à 15 minutes.
Vous aurez besoin de votre RIB ainsi que de votre téléphone portable pour signer électroniquement.

Une fois l’adhésion terminée, n’oubliez pas de prendre contact avec votre LGO afin d’activer le module en caisse.')
            ->setTextUnderLGO("Si votre LGO Prestataire n'est pas partenaire, vos ventes ne seront pas enregistrées automatiquement. Pour plus d'informations, contactez <EMAIL>")
            ->setTextBeforeSignature('Le programme Carte Privilège de PHARM-UPP vous garantit être propriétaire de votre base de données clients. 
La société Aquitem agit en prestataire de gestion de données et respecte scrupuleusement les directives RGPD.')
            ->setTextAfterSignature("Bienvenue dans le programme Carte Privilège de PHARM-UPP

Vous recevrez prochainement votre Welcome Pack de 500 Cartes Privilège pour débuter l’encartage ainsi que vos options demandées.

Vous pouvez désormais vous rapprocher de votre LGO afin de demander l’activation du module en caisse.

Dès validation de l'adhésion par nos services, le code RUM (Référence Unique de Mandat) vous sera communiqué.")
            ->setTextContact('En cas de difficulté, contactez-nous à l’adresse suivante : <EMAIL>')

            ->setColor1('#009d72')
            ->setColor2('#2c53a0')
            ->setColor3('#009d72')

            ->setEmailSenderEmail('<EMAIL>')
            ->setEmailSenderName('Playwright')

            ->setEmailAquitemEnabled(false)
            ->setEmailAquitemAttachContract(false)
            ->setEmailAquitemRecipientEmail('<EMAIL>')
            ->setEmailAquitemSubject('Sujet')
            ->setEmailAquitemContent('<EMAIL>')

            ->setEmailEnseigneEnabled(false)
            ->setEmailEnseigneAttachContract(false)
            ->setEmailEnseigneRecipientEmail('<EMAIL>')
            ->setEmailEnseigneSubject('Sujet')
            ->setEmailEnseigneContent('<EMAIL>')

            ->setEmailSubscriberEnabled(false)
            ->setEmailSubscriberAttachContract(false)
            ->setEmailSubscriberSubject('Sujet')
            ->setEmailSubscriberContent('<EMAIL>')
            ->setLinkMobilityAccp('')

            ->addOption($option1)
            ->addOption($option2)
        ;

        $section = (new FormSection())
            ->setTitle('Informations générales')
            ->setPosition(1);

        $section->addField(new FormField()
            ->setLabel('Votre numéro FINESS')
            ->setType('finess')
            ->setRequired(true)
            ->setHelp('N° commençant par le numéro de votre département et suivi du chiffre 2 (ex : 332123456)')
            ->setPosition(1)
        );

        $section->addField(new FormField()
            ->setLabel('Adresse e-mail du responsable')
            ->setType('managerEmail')
            ->setRequired(true)
            ->setHelp('Vous recevrez par e-mail le contrat signé à la fin de la procédure d\'adhésion')
            ->setPosition(2)
        );

        $section->addField(new FormField()
            ->setLabel('Code ID PHARM-UPP')
            ->setType('businessCode')
            ->setRequired(true)
            ->setHelp('Code composé de 8 caractères commençant par F000')
            ->setPosition(3)
        );

        $section->addField(new FormField()
            ->setLabel('SIRET')
            ->setType('siret')
            ->setRequired(true)
            ->setHelp('Le numéro SIRET doit contenir exactement 14 chiffres')
            ->setPosition(4)
        );

        // date d'entrée
        $section->addField(new FormField()
            ->setLabel("Date souhaitée d'entrée dans le programme")
            ->setType('dateEntree')
            ->setRequired(true)
            ->setHelp('Déploiement sous 3 semaines')
            ->setPosition(5)
        );

        $section->setName();

        foreach ($section->getFields() as $field) {
            $field->setName();
        }

        $enseigne->addSection($section);

        return $enseigne;
    }

    public static function getGroups(): array
    {
        return ['playwright'];
    }
}
