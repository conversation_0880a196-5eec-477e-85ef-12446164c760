<?php

namespace App\DataFixtures;

use App\Story\DefaultStory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

class AppFixtures extends Fixture implements FixtureGroupInterface
{
    public function load(ObjectManager $manager): void
    {
        DefaultStory::load();
    }

    public function getDependencies(): array
    {
        return [];
    }

    public static function getGroups(): array
    {
        return ['default'];
    }
}
