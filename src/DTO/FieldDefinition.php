<?php

namespace App\DTO;

use App\Entity\Enseigne;

class FieldDefinition
{
    private function __construct(
        public string $group,
        public string $icon,
        public string $label,
        public string $formType,
        public FieldConfig $config,
        public array $formConfig = [],
        public array $poles = [],
        public bool $required = false,
    ) {
        if (empty($this->poles)) {
            $this->poles = array_values(Enseigne::POLES);
        }
    }

    public static function formatted(
        string $icon,
        string $label,
        string $formType,
        FieldConfig $config,
        array $formConfig = [],
        array $poles = [],
        bool $required = false,
    ): FieldDefinition {
        return new self(
            group: 'formatted',
            icon: $icon,
            label: $label,
            formType: $formType,
            config: $config,
            formConfig: $formConfig,
            poles: $poles,
            required: $required,
        );
    }

    public static function free(
        string $icon,
        string $label,
        string $formType,
        FieldConfig $config,
        array $formConfig = [],
        array $poles = [],
        bool $required = false,
    ): FieldDefinition {
        return new self(
            group: 'free',
            icon: $icon,
            label: $label,
            formType: $formType,
            config: $config,
            formConfig: $formConfig,
            poles: $poles,
            required: $required,
        );
    }
}
