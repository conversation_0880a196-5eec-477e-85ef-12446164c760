<?php

namespace App\Story;

use App\Entity\Enseigne;
use App\Entity\PdfDisplayConfig;
use App\Factory\EnseigneFactory;
use App\Factory\FormFieldFactory;
use App\Factory\FormSectionFactory;
use App\Factory\OptionFactory;
use Zenstruck\Foundry\Story;

final class DefaultStory extends Story
{
    public function build(): void
    {
        self::createDemoEnseigne();
        $this->createFormWithEveryFieldsEnseigne();
    }

    public static function createDemoEnseigne(string $identifier = 'X456B73'): Enseigne
    {
        // Création de l'enseigne de test
        return EnseigneFactory::new()
            ->create([
                'name' => 'Cartes Privilège PHARM-UPP',
                'identifier' => $identifier,
                'logo' => 'sample-logo.png',
                'contract' => 'sample-contract.pdf',
                'pole' => Enseigne::POLE_SANTE,
                'contractFilename' => 'UPP_{informations-generales-votre-numero-finess}_{signature-date}',
                'textPresentation' => "Pour adhérer au programme Cartes Privilège PHARM-UPP, suivez les 5 étapes de la procédure d'adhésion.
Elle doit s'effectuer en une seule fois et prend environ 10 à 15 minutes.
Vous aurez besoin de votre RIB ainsi que de votre téléphone portable pour signer électroniquement.

Une fois l'adhésion terminée, n'oubliez pas de prendre contact avec votre LGO afin d'activer le module en caisse.",
                'textUnderLGO' => "Si votre LGO Prestataire n'est pas partenaire, vos ventes ne seront pas enregistrées automatiquement. Pour plus d'informations, contactez <EMAIL>",
                'textBeforeSignature' => 'Le programme Carte Privilège de PHARM-UPP vous garantit être propriétaire de votre base de données clients. 
La société Aquitem agit en prestataire de gestion de données et respecte scrupuleusement les directives RGPD.',
                'textAfterSignature' => "Bienvenue dans le programme Carte Privilège de PHARM-UPP

Vous recevrez prochainement votre Welcome Pack de 500 Cartes Privilège pour débuter l'encartage ainsi que vos options demandées.

Vous pouvez désormais vous rapprocher de votre LGO afin de demander l'activation du module en caisse.

Dès validation de l'adhésion par nos services, le code RUM (Référence Unique de Mandat) vous sera communiqué.",
                'textContact' => "En cas de difficulté, contactez-nous à l'adresse suivante : <EMAIL>",
                'color1' => '#5ABEBD',
                'color2' => '#5ABEBD',
                'color3' => '#5ABEBD',
                'askForSepa' => true,
                'emailSenderEmail' => '<EMAIL>',
                'emailSenderName' => 'Cartes Privilège PHARM-UPP',
                'emailAquitemEnabled' => true,
                'emailAquitemAttachContract' => true,
                'emailAquitemRecipientEmail' => '<EMAIL>',
                'emailAquitemSubject' => '[{informations-generales-votre-numero-finess}] Adhésion Cartes Privilège PHARM-UPP : {coordonnees-votre-libelle-de-pharmacie-ex-pharmacie-de-la-poste}',
                'emailAquitemContent' => 'Bonjour',
                'emailEnseigneEnabled' => true,
                'emailEnseigneAttachContract' => true,
                'emailEnseigneRecipientEmail' => '<EMAIL>',
                'emailEnseigneSubject' => '[{informations-generales-votre-numero-finess}] Adhésion Cartes Privilège PHARM-UPP : {coordonnees-votre-libelle-de-pharmacie-ex-pharmacie-de-la-poste}',
                'emailEnseigneContent' => 'Bonjour',
                'emailSubscriberEnabled' => true,
                'emailSubscriberAttachContract' => true,
                'emailSubscriberSubject' => 'Programme Cartes Privilège',
                'emailSubscriberContent' => 'Bonjour',
                'options' => OptionFactory::new()->sequence([
                    [
                        'name' => 'Welcome Pack : 500 Cartes Privilège + Kit PLV',
                        'text' => "Offert à l'inscription",
                        'hasQuantity' => false,
                        'isFree' => true,
                        'position' => 1,
                    ],
                    [
                        'name' => 'Pack supplémentaire de 500 Cartes Privilège – 135€HT hors frais d’expédition',
                        'text' => "Les frais d'expédition seront facturés à la pharmacie. 
Les factures émises par AQUITEM seront réglées exclusivement par prélèvement à 30 jours date de facture. Elles seront envoyées à l'adresse e-mail des officines.",
                        'hasQuantity' => true,
                        'unitPrice' => 135,
                        'isFree' => false,
                        'position' => 2,
                    ],
                ]),
                'sections' => FormSectionFactory::new()->sequence([
                    [
                        'title' => 'Informations générales',
                        'position' => 1,
                        'fields' => FormFieldFactory::new()->sequence([
                            [
                                'label' => 'Votre numéro FINESS',
                                'type' => 'finess',
                                'required' => true,
                                'help' => 'N° commençant par le numéro de votre département et suivi du chiffre 2',
                                'position' => 1,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true),
                            ],
                            [
                                'label' => 'Votre code ID PHARM-UPP',
                                'type' => 'codePharmupp',
                                'required' => true,
                                'help' => 'Code composé de 8 caractères commençant par F000',
                                'position' => 2,
                            ],
                            [
                                'label' => 'SIRET',
                                'type' => 'siret',
                                'required' => true,
                                'position' => 3,
                            ],
                            [
                                'label' => 'Raison sociale',
                                'type' => 'text',
                                'required' => true,
                                'position' => 4,
                            ],
                            [
                                'label' => "Date souhaitée d'entrée dans le programme",
                                'type' => 'dateEntree',
                                'required' => true,
                                'help' => 'Déploiement sous 3 semaines en fonction des LGO.',
                                'position' => 5,
                            ],
                            [
                                'label' => 'Votre statut',
                                'type' => 'select',
                                'required' => true,
                                'options' => [
                                    'choices' => [
                                        'Essentiel' => 'Essentiel',
                                        'Ultra' => 'Ultra',
                                    ],
                                ],
                                'position' => 6,
                            ],
                        ]),
                    ],
                    [
                        'title' => 'Coordonnées',
                        'description' => 'Ces informations seront utilisées dans les communications',
                        'position' => 2,
                        'fields' => FormFieldFactory::new()->sequence([
                            [
                                'label' => 'Votre libellé de pharmacie (ex : Pharmacie de la poste)',
                                'type' => 'libellePharmacie',
                                'required' => true,
                                'help' => '(26 caractères maximum, espace compris)',
                                'position' => 1,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true),
                            ],
                            [
                                'label' => 'Adresse e-mail de la pharmacie',
                                'type' => 'email',
                                'required' => false,
                                'position' => 2,
                            ],
                            [
                                'label' => 'Site internet de la pharmacie',
                                'type' => 'url',
                                'required' => false,
                                'position' => 3,
                            ],
                            [
                                'label' => 'Adresse',
                                'type' => 'text',
                                'required' => true,
                                'position' => 4,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true, signature: true),
                            ],
                            [
                                'label' => "Complément d'adresse",
                                'type' => 'text',
                                'required' => false,
                                'position' => 5,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true, signature: true),
                            ],
                            [
                                'label' => 'Code postal',
                                'type' => 'text',
                                'required' => true,
                                'position' => 6,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true, signature: true),
                            ],
                            [
                                'label' => 'Ville',
                                'type' => 'text',
                                'required' => true,
                                'position' => 7,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true, signature: true),
                            ],
                            [
                                'label' => 'Pays',
                                'type' => 'country',
                                'required' => true,
                                'position' => 8,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true, signature: true),
                            ],
                            [
                                'label' => 'Tél. fixe',
                                'type' => 'tel',
                                'required' => true,
                                'position' => 9,
                                'pdfDisplayConfig' => new PdfDisplayConfig(summary: true),
                            ],
                            [
                                'label' => 'Fax',
                                'type' => 'tel',
                                'required' => false,
                                'position' => 10,
                            ],
                        ]),
                    ],
                    [
                        'title' => 'Responsable de la pharmacie',
                        'position' => 3,
                        'fields' => FormFieldFactory::new()->sequence([
                            [
                                'label' => 'Civilité',
                                'type' => 'radio',
                                'required' => true,
                                'options' => [
                                    'choices' => [
                                        'Mme.' => 'Mme.',
                                        'M.' => 'M.',
                                    ],
                                ],
                                'position' => 1,
                            ],
                            [
                                'label' => 'Prénom',
                                'type' => 'text',
                                'required' => true,
                                'position' => 2,
                                'pdfDisplayConfig' => new PdfDisplayConfig(signature: true),
                            ],
                            [
                                'label' => 'Nom',
                                'type' => 'text',
                                'required' => true,
                                'position' => 3,
                                'pdfDisplayConfig' => new PdfDisplayConfig(signature: true),
                            ],
                            [
                                'label' => 'Tél. portable',
                                'type' => 'tel',
                                'required' => true,
                                'help' => 'Pour signer électroniquement ce contrat, vous devez avoir accès à ce téléphone afin de recevoir un code de confirmation',
                                'position' => 4,
                            ],
                            [
                                'label' => 'Adresse e-mail',
                                'type' => 'managerEmail',
                                'required' => true,
                                'help' => "Vous recevrez par e-mail le contrat signé à la fin de la procédure d'adhésion",
                                'position' => 5,
                            ],
                        ]),
                    ],
                    [
                        'title' => 'Responsable de la comptabilité',
                        'position' => 4,
                        'fields' => FormFieldFactory::new()->sequence([
                            [
                                'label' => 'Civilité',
                                'type' => 'radio',
                                'required' => true,
                                'options' => [
                                    'choices' => [
                                        'Mme.' => 'Mme.',
                                        'M.' => 'M.',
                                    ],
                                ],
                                'position' => 1,
                            ],
                            [
                                'label' => 'Prénom',
                                'type' => 'text',
                                'required' => true,
                                'position' => 2,
                            ],
                            [
                                'label' => 'Nom',
                                'type' => 'text',
                                'required' => true,
                                'position' => 3,
                            ],
                            [
                                'label' => 'Tél.',
                                'type' => 'tel',
                                'required' => true,
                                'position' => 4,
                            ],
                            [
                                'label' => 'Adresse e-mail de réception des factures',
                                'type' => 'email',
                                'required' => true,
                                'help' => 'Vos factures vous seront adressées en format numérique (dématérialisées).<br>Pour toutes informations concernant votre facture dématérialisée, adressez votre demande à <EMAIL>',
                                'position' => 5,
                            ],
                        ]),
                    ],
                    [
                        'title' => 'Votre prestataire informatique',
                        'position' => 5,
                        'fields' => FormFieldFactory::new()->sequence([
                            [
                                'label' => 'Votre prestataire',
                                'type' => 'select',
                                'required' => true,
                                'options' => [
                                    'choices' => [
                                        'LGPI/ Pharmagest' => 'LGPI/ Pharmagest',
                                        'SMART RX' => 'SMART RX',
                                        'Winpharma' => 'Winpharma',
                                        'Léo' => 'Léo',
                                        'Autre' => 'Autre',
                                    ],
                                ],
                                'position' => 1,
                            ],
                            [
                                'label' => 'Autre',
                                'type' => 'text',
                                'required' => false,
                                'position' => 2,
                            ],
                        ]),
                    ],
                ]),
            ]);
    }

    public function createFormWithEveryFieldsEnseigne(): void
    {
        // Création de l'enseigne de test
        $enseigne = EnseigneFactory::new()
            ->create([
                'name' => 'Enseigne avec tous les champs',
                'identifier' => 'CHAMPS',
                'pole' => Enseigne::POLE_SANTE,
            ]);

        $formattedSection = FormSectionFactory::new()->create([
            'title' => 'Tous les champs formatés',
            'description' => 'Cette section contient un exemple de chaque type de champ formaté disponible',
            'position' => 1,
            'enseigne' => $enseigne,
        ]);

        $freeSection = FormSectionFactory::new()->create([
            'title' => 'Tous les champs libres',
            'description' => 'Cette section contient un exemple de chaque type de champ libre disponible',
            'position' => 1,
            'enseigne' => $enseigne,
        ]);

        FormFieldFactory::new()->sequence([
            // Champs formatés
            [
                'label' => 'Exemple SIRET',
                'type' => 'siret',
                'required' => true,
                'help' => 'Le numéro SIRET doit contenir exactement 14 chiffres',
                'position' => 1,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple FINESS',
                'type' => 'finess',
                'required' => true,
                'help' => 'N° commençant par le numéro de votre département et suivi du chiffre 2 (ex : 332123456)',
                'position' => 2,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple Code PHARM-UPP',
                'type' => 'codePharmupp',
                'required' => true,
                'help' => 'Code composé de 8 caractères commençant par F000',
                'position' => 3,
                'section' => $formattedSection,
            ],

            [
                'label' => 'Code Zefid : Site + PDV',
                'type' => 'codeZefid',
                'required' => true,
                'help' => 'Code composé de 6 caractères',
                'position' => 16,
                'section' => $formattedSection,
            ],

            [
                'label' => 'Exemple TVA Intracommunautaire',
                'type' => 'tvaIntra',
                'required' => true,
                'help' => '2 lettres + 12 chiffres maximum',
                'position' => 17,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple ID',
                'type' => 'id',
                'required' => true,
                'help' => 'Code composé de 6 chiffres',
                'position' => 18,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple Date d\'entrée',
                'type' => 'dateEntree',
                'required' => true,
                'help' => 'Déploiement sous 3 semaines',
                'position' => 19,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple Numéro Ivrylab',
                'type' => 'numeroIvrylab',
                'required' => true,
                'help' => '7 caractères sans espace',
                'position' => 20,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple GUID Titulaire',
                'type' => 'guidTitulaire',
                'required' => true,
                'help' => 'Ex: 33d738b0-679f-48cc-aaec-29671f08c67d',
                'position' => 21,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple GUID Équipe',
                'type' => 'guidEquipe',
                'required' => true,
                'help' => 'Ex: 72b7c79a-b696-4fd5-a078-a860aa1b8f24',
                'position' => 22,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Exemple Nombre de Parfumeries',
                'type' => 'nbParfumeries',
                'required' => true,
                'help' => 'Nombre entre 1 et 999',
                'position' => 23,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Adresse e-mail',
                'type' => 'managerEmail',
                'required' => true,
                'help' => "Vous recevrez par e-mail le contrat signé à la fin de la procédure d'adhésion",
                'position' => 24,
                'section' => $formattedSection,
            ],
            [
                'label' => 'Votre libellé de pharmacie (ex : Pharmacie de la poste)',
                'type' => 'libellePharmacie',
                'required' => true,
                'help' => '(26 caractères maximum, espace compris)',
                'position' => 25,
                'section' => $formattedSection,
            ],

            // Champs libres
            [
                'label' => 'Exemple Texte',
                'type' => 'text',
                'required' => true,
                'help' => 'Un champ texte simple',
                'position' => 4,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Numérique',
                'type' => 'numeric',
                'required' => true,
                'help' => 'Un champ pour les nombres',
                'position' => 5,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Téléphone',
                'type' => 'tel',
                'required' => true,
                'help' => 'Un champ pour les numéros de téléphone',
                'position' => 6,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Email',
                'type' => 'email',
                'required' => true,
                'help' => 'Un champ pour les adresses email',
                'position' => 7,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Date',
                'type' => 'date',
                'required' => true,
                'help' => 'Un champ pour sélectionner une date',
                'position' => 8,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Radio',
                'type' => 'radio',
                'required' => true,
                'help' => 'Des boutons radio',
                'options' => [
                    'choices' => [
                        'Option 1' => 'option_1',
                        'Option 2' => 'option_2',
                        'Option 3' => 'option_3',
                    ],
                ],
                'position' => 9,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Radio Non obligatoire',
                'type' => 'radio',
                'required' => false,
                'help' => 'Des autres boutons radio',
                'options' => [
                    'choices' => [
                        'Option 1' => 'option_1',
                        'Option 2' => 'option_2',
                        'Option 3' => 'option_3',
                    ],
                ],
                'position' => 9,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Cases à cocher',
                'type' => 'checkboxes',
                'required' => true,
                'help' => 'Des cases à cocher multiples',
                'options' => [
                    'choices' => [
                        'Choix 1' => 'choix_1',
                        'Choix 2' => 'choix_2',
                        'Choix 3' => 'choix_3',
                    ],
                ],
                'position' => 10,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Case à cocher obligatoire',
                'type' => 'checkbox',
                'required' => true,
                'help' => 'Une case à cocher qui doit être cochée pour continuer',
                'position' => 11,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Liste déroulante',
                'type' => 'checkboxes',
                'required' => true,
                'help' => 'Une liste déroulante',
                'options' => [
                    'choices' => [
                        'Choix 1' => 'choix_1',
                        'Choix 2' => 'choix_2',
                        'Choix 3' => 'choix_3',
                    ],
                ],
                'position' => 12,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Région',
                'type' => 'region',
                'required' => true,
                'help' => 'Sélection d\'une région française',
                'position' => 13,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple URL',
                'type' => 'url',
                'required' => true,
                'help' => 'Un champ pour les URL',
                'position' => 14,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Pays',
                'type' => 'country',
                'required' => true,
                'help' => 'Sélection d\'un pays',
                'position' => 15,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Coordonnées',
                'type' => 'gpsCoordinates',
                'required' => true,
                'help' => 'Format: latitude,longitude (ex: 48.8566,2.3522 pour Paris)',
                'position' => 15,
                'section' => $freeSection,
            ],
            [
                'label' => 'Exemple Ville',
                'type' => 'city',
                'required' => true,
                'help' => 'Saisissez le nom de la ville pour obtenir des suggestions',
                'position' => 16,
                'section' => $freeSection,
            ],
        ])->create();
    }
}
