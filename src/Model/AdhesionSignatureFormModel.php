<?php

namespace App\Model;

use Symfony\Component\Validator\Constraints as Assert;

class AdhesionSignatureFormModel
{
    public ?\DateTime $signatureDate = null;

    public bool $signed = false;

    public bool $cgvAccepted = false;

    public bool $sepaAccepted = false;

    public bool $debitAccepted = false;

    public ?string $signatureData = null;

    #[Assert\Length(exactly: 4)]
    #[Assert\NotBlank()]
    #[Assert\EqualTo(propertyPath: 'requiredPin', message: 'Le code PIN saisi est invalide')]
    public string $pin;

    public string $requiredPin;

    public \DateTime $pinSentDate;
}
