<?php

namespace App\Model;

use Symfony\Component\Intl\Countries;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Bic;

class AdhesionSepaFormModel
{
    #[Assert\Iban()]
    public ?string $iban = null;

    #[Assert\When(
        expression: 'this.iban !== null',
        constraints: [
            new Bic(ibanPropertyPath: 'iban'),
        ],
    )]
    public string $bic;

    public string $signaturePhone;

    /** Informations pour le PDF */
    public ?string $sepaName = null;
    public ?string $address = null;
    public ?string $address2 = null;
    public ?string $zipCode = null;
    public ?string $city = null;
    public ?string $country = 'FR';

    public function ibanFormatted()
    {
        return wordwrap($this->iban, 4, ' ', true);
    }

    public function getCountryName()
    {
        return Countries::getName($this->country);
    }
}
