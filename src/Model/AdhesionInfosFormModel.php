<?php

namespace App\Model;

use App\Entity\Enseigne;

class AdhesionInfosFormModel
{
    /** @var array<string,mixed> */
    public array $dynamicFields = [];

    public function hasDynamicValue(string $fieldName): bool
    {
        return array_key_exists($fieldName, $this->dynamicFields);
    }

    public function getDynamicValue(string $fieldName): mixed
    {
        return $this->dynamicFields[$fieldName] ?? null;
    }

    public function getDynamicValueFormatted(string $fieldName): mixed
    {
        $value = $this->getDynamicValue($fieldName);

        if ($value instanceof \DateTime) {
            return $value->format('d/m/Y');
        }

        if (is_array($value)) {
            return join(', ', $value);
        }

        return $value;
    }

    public function getRequiredValue(Enseigne $enseigne, string $fieldType): mixed
    {
        $field = $enseigne->getFieldByType($fieldType);

        return $this->getDynamicValueFormatted($field->getName());
    }
}
