<?php

namespace App\Twig;

use Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class EmailLinkExtension extends AbstractExtension
{
    public function __construct(
        private readonly HtmlSanitizerInterface $appLinkSanitizer,
    ) {
    }

    public function getFilters(): array
    {
        return [
            new TwigFilter('emailLink', $this->formatEmailLink(...), ['is_safe' => ['html']]),
        ];
    }

    public function formatEmailLink(?string $text): ?string
    {
        if (null === $text) {
            return $text;
        }

        $search = ['/<p>__<\/p>/', '/([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4})/'];
        $replace = ['<hr />', '<a href="mailto:$1">$1</a>'];
        $text = preg_replace($search, $replace, $text);

        return $this->appLinkSanitizer->sanitize($text);
    }
}
