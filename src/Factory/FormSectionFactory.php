<?php

namespace App\Factory;

use App\Entity\FormSection;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<FormSection>
 */
final class FormSectionFactory extends PersistentProxyObjectFactory
{
    public static function class(): string
    {
        return FormSection::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     */
    protected function defaults(): array|callable
    {
        return [
            'enseigne' => EnseigneFactory::new(),
            'position' => self::faker()->randomNumber(),
            'title' => self::faker()->words(3, true),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(FormSection $formSection): void {})
        ;
    }
}
