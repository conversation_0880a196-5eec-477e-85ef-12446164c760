<?php

namespace App\Factory;

use App\Entity\Enseigne;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Enseigne>
 */
final class EnseigneFactory extends PersistentProxyObjectFactory
{
    public static function class(): string
    {
        return Enseigne::class;
    }

    protected function defaults(): array|callable
    {
        return [
            // Identification
            'name' => self::faker()->company(),
            'logo' => 'sample-logo.png',
            'identifier' => self::faker()->bothify('???###'),
            'contract' => 'sample-contract.pdf',
            'pole' => self::faker()->randomElement(array_values(Enseigne::POLES)),
            // Options
            'options' => OptionFactory::new()->many(self::faker()->numberBetween(1, 3)),
            // Customization
            'textPresentation' => self::faker()->paragraphs(2, true),
            'textUnderLGO' => self::faker()->paragraph(),
            'textBeforeSignature' => self::faker()->paragraph(),
            'textAfterSignature' => self::faker()->paragraph(),
            'textContact' => self::faker()->text(),
            'color1' => self::faker()->hexColor(),
            'color2' => self::faker()->hexColor(),
            'color3' => self::faker()->hexColor(),
            'contractFilename' => 'contract_{finess}_{date}.pdf',
            'linkMobilityAccp' => self::faker()->bothify('AGSI01_#########'),
            // Facturation
            'askForSepa' => self::faker()->boolean(),
            // Notifications
            'emailSenderEmail' => self::faker()->companyEmail(),
            'emailSenderName' => self::faker()->company(),
            'emailAquitemEnabled' => self::faker()->boolean(),
            'emailAquitemAttachContract' => self::faker()->boolean(),
            'emailAquitemRecipientEmail' => self::faker()->email(),
            'emailAquitemSubject' => self::faker()->sentence(),
            'emailAquitemContent' => self::faker()->paragraphs(3, true),
            'emailEnseigneEnabled' => self::faker()->boolean(),
            'emailEnseigneAttachContract' => self::faker()->boolean(),
            'emailEnseigneRecipientEmail' => self::faker()->email(),
            'emailEnseigneSubject' => self::faker()->sentence(),
            'emailEnseigneContent' => self::faker()->paragraphs(3, true),
            'emailSubscriberEnabled' => self::faker()->boolean(),
            'emailSubscriberAttachContract' => self::faker()->boolean(),
            'emailSubscriberSubject' => self::faker()->sentence(),
            'emailSubscriberContent' => self::faker()->paragraphs(3, true),
            // Other
            'formCompleted' => true,
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Enseigne $enseigne): void {})
        ;
    }
}
