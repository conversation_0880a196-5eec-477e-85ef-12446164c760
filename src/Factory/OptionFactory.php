<?php

namespace App\Factory;

use App\Entity\Option;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Option>
 */
final class OptionFactory extends PersistentProxyObjectFactory
{
    public static function class(): string
    {
        return Option::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        return [
            'enseigne' => EnseigneFactory::new(),
            'hasQuantity' => self::faker()->boolean(),
            'isFree' => self::faker()->boolean(),
            'name' => self::faker()->text(255),
            'noPriceDisplayed' => self::faker()->boolean(),
            'position' => self::faker()->randomNumber(),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Option $option): void {})
        ;
    }
}
