<?php

namespace App\Factory;

use App\Entity\FormField;
use App\Entity\PdfDisplayConfig;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<FormField>
 */
final class FormFieldFactory extends PersistentProxyObjectFactory
{
    public static function class(): string
    {
        return FormField::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        return [
            'label' => self::faker()->text(255),
            'position' => self::faker()->randomNumber(),
            'required' => self::faker()->boolean(),
            'section' => FormSectionFactory::new(),
            'type' => self::faker()->text(255),
            'pdfDisplayConfig' => new PdfDisplayConfig(),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(FormField $formField): void {})
        ;
    }
}
