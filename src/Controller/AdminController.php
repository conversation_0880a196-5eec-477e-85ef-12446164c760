<?php

namespace App\Controller;

use App\Entity\Enseigne;
use App\Form\EnseigneType;
use App\Service\FileUploader;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class AdminController extends AbstractController
{
    #[Route('/admin/')]
    public function index(): Response
    {
        return $this->redirectToRoute('app_admin_enseigne_liste');
    }

    #[Route('/admin/enseigne/create-old', name: 'app_admin_enseigne_create_old')]
    public function create(Request $request, EntityManagerInterface $entityManager, FileUploader $fileUploader): Response
    {
        $enseigne = new Enseigne();

        $form = $this->createForm(EnseigneType::class, $enseigne, ['edit' => false]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $fileUploader->handleForm($form, $enseigne);
            $entityManager->persist($enseigne);
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_liste');
        }

        return $this->render('admin/create.html.twig', [
            'form' => $form,
            'enseigne' => $enseigne,
        ]);
    }

    #[Route('/admin/enseigne/edit-old/{id}', name: 'app_admin_enseigne_edit_old')]
    public function edit(Enseigne $enseigne, Request $request, EntityManagerInterface $entityManager, FileUploader $fileUploader): Response
    {
        $form = $this->createForm(EnseigneType::class, $enseigne, ['edit' => true]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $fileUploader->handleForm($form, $enseigne);
            $entityManager->persist($enseigne);
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_liste');
        }

        return $this->render('admin/create.html.twig', [
            'form' => $form,
            'enseigne' => $enseigne,
        ]);
    }
}
