<?php

namespace App\Controller;

use App\Entity\Enseigne;
use App\Service\DataMocker;
use App\Service\FormSessionHandler;
use App\Service\PdfGenerator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class PdfController extends AbstractController
{
    private const COND_ENV_DEV = 'env("APP_ENV") === "dev"';

    #[Route('/pdf/contract/{identifier}', name: 'app_pdf_contract')]
    public function testFull(Enseigne $enseigne, FormSessionHandler $formSessionHandler, PdfGenerator $pdfGenerator): Response
    {
        $adhesion = $formSessionHandler->restoreAdhesion();

        $adhesion->signature->signatureDate = new \DateTime();

        return (new BinaryFileResponse(
            $pdfGenerator->getFullPdf($enseigne, $adhesion),
        ))->setContentDisposition('inline', 'file.pdf');
    }

    /*** ROUTES DE PREVIEW HTML UNIQUEMENT ACCESSIBLES EN ENV DE DEV ***/
    #[Route('/pdf/debug-footer/{identifier}', name: 'app_pdf_debug_footer', condition: self::COND_ENV_DEV)]
    public function footer(Enseigne $enseigne): Response
    {
        return $this->render('pdf/footer.html.twig', [
            'enseigne' => $enseigne,
        ]);
    }

    #[Route('/pdf/debug-contract/{identifier}', name: 'app_pdf_debug_contract', condition: self::COND_ENV_DEV)]
    public function list(Enseigne $enseigne, FormSessionHandler $formSessionHandler, Request $request): Response
    {
        if ($request->query->has('session')) {
            $adhesion = $formSessionHandler->restoreAdhesion();
        } else {
            $adhesion = DataMocker::mockAdhesion($enseigne);
        }

        return $this->render('pdf/contract.html.twig', [
            'enseigne' => $enseigne,
            'adhesion' => $adhesion,
        ]);
    }

    #[Route('/pdf/debug-sepa/{identifier}', name: 'app_pdf_debug_sepa', condition: self::COND_ENV_DEV)]
    public function testSepaRender(Enseigne $enseigne, FormSessionHandler $formSessionHandler, PdfGenerator $pdfGenerator, Request $request): Response
    {
        if ($request->query->has('session')) {
            $adhesion = $formSessionHandler->restoreAdhesion();
        } else {
            $adhesion = DataMocker::mockAdhesion($enseigne);
        }

        $pdf = $pdfGenerator->getSepaPdf($enseigne, $adhesion);

        return (new BinaryFileResponse(
            $pdf,
        ))->setContentDisposition('inline', 'file.pdf');
    }
}
