<?php

namespace App\Controller;

use App\DataFixtures\PlaywrightFixtures;
use App\Entity\Enseigne;
use App\Service\DataMocker;
use App\Service\TestManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;

class TestController extends AbstractController
{
    #[Route('/test/start', name: 'app_test_start')]
    public function start(PlaywrightFixtures $fixtures, EntityManagerInterface $entityManager, TestManager $testManager): Response
    {
        if (!$testManager->isTesting()) {
            throw new NotFoundHttpException();
        }

        $fixtures->load($entityManager);
        $enseigne = $entityManager->getRepository(Enseigne::class)->findOneByIdentifier(PlaywrightFixtures::TEST_IDENTIFIER);

        return new JsonResponse(DataMocker::mockAdhesion($enseigne));
    }

    #[Route('/test/clean', name: 'app_test_clean')]
    public function clean(PlaywrightFixtures $fixtures, TestManager $testManager): Response
    {
        if (!$testManager->isTesting()) {
            throw new NotFoundHttpException();
        }

        $fixtures->clean();

        return new JsonResponse([]);
    }
}
