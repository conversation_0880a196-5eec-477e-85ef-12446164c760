<?php

namespace App\Controller\Admin;

use App\Entity\Enseigne;
use App\Form\Enseigne\EnseigneCustomizationType;
use App\Form\Enseigne\EnseigneFacturationType;
use App\Form\Enseigne\EnseigneFormulaireDynamiqueType;
use App\Form\Enseigne\EnseigneIdentificationType;
use App\Form\Enseigne\EnseigneNotificationsType;
use App\Form\Enseigne\EnseigneOptionsType;
use App\Service\FileUploader;
use App\Service\FormDefinitions;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/enseigne')]
class EnseigneController extends AbstractController
{
    #[Route('/list', name: 'app_admin_enseigne_liste')]
    public function list(EntityManagerInterface $entityManager): Response
    {
        return $this->render('admin/index.html.twig', [
            'enseignes' => $entityManager->getRepository(Enseigne::class)->findAll(),
        ]);
    }

    #[Route('/create', name: 'app_admin_enseigne_create')]
    public function create(EntityManagerInterface $entityManager): Response
    {
        $enseigne = new Enseigne();
        $entityManager->persist($enseigne);
        $entityManager->flush();

        return $this->redirectToRoute('app_admin_enseigne_1_identification', [
            'id' => $enseigne->getId(),
        ]);
    }

    #[Route('/edit/{id}', name: 'app_admin_enseigne_edit')]
    public function edit(Enseigne $enseigne): Response
    {
        return $this->redirectToRoute('app_admin_enseigne_1_identification', [
            'id' => $enseigne->getId(),
        ]);
    }

    #[Route('/delete/{id}', name: 'app_admin_enseigne_delete')]
    public function delete(Enseigne $enseigne, EntityManagerInterface $entityManager): Response
    {
        $entityManager->remove($enseigne);
        $entityManager->flush();

        return $this->redirectToRoute('app_admin_enseigne_liste');
    }

    #[Route('/{id}/identification', name: 'app_admin_enseigne_1_identification')]
    public function identification(Request $request, Enseigne $enseigne, EntityManagerInterface $entityManager, FileUploader $fileUploader): Response
    {
        $form = $this->createForm(EnseigneIdentificationType::class, $enseigne, [
            'edit' => !is_null($enseigne->getLogo()), // Si il y a un logo c'est que cette page a déjà été validée
        ]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $fileUploader->handleForm($form, $enseigne);
            $entityManager->persist($enseigne);
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_1_options', ['id' => $enseigne->getId()]);
        }

        return $this->render('admin/enseigne/1-1-identification.html.twig', [
            'form' => $form,
            'enseigne' => $enseigne,
            'current_step' => 1,
            'current_substep' => 1,
        ]);
    }

    #[Route('/{id}/options', name: 'app_admin_enseigne_1_options')]
    public function options(Request $request, Enseigne $enseigne, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(EnseigneOptionsType::class, $enseigne);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_1_customization', ['id' => $enseigne->getId()]);
        }

        return $this->render('admin/enseigne/1-2-options.html.twig', [
            'form' => $form,
            'enseigne' => $enseigne,
            'current_step' => 1,
            'current_substep' => 2,
        ]);
    }

    #[Route('/{id}/customization', name: 'app_admin_enseigne_1_customization')]
    public function customization(Request $request, Enseigne $enseigne, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(EnseigneCustomizationType::class, $enseigne);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_1_facturation', ['id' => $enseigne->getId()]);
        }

        return $this->render('admin/enseigne/1-3-customization.html.twig', [
            'form' => $form,
            'enseigne' => $enseigne,
            'current_step' => 1,
            'current_substep' => 3,
        ]);
    }

    #[Route('/{id}/facturation', name: 'app_admin_enseigne_1_facturation')]
    public function facturation(Request $request, Enseigne $enseigne, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(EnseigneFacturationType::class, $enseigne);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_2_form', ['id' => $enseigne->getId()]);
        }

        return $this->render('admin/enseigne/1-4-facturation.html.twig', [
            'enseigne' => $enseigne,
            'form' => $form,
            'current_step' => 1,
            'current_substep' => 4,
        ]);
    }

    #[Route('/{id}/form', name: 'app_admin_enseigne_2_form')]
    public function form(Request $request, Enseigne $enseigne, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(EnseigneFormulaireDynamiqueType::class, $enseigne);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_enseigne_3_notifications', ['id' => $enseigne->getId()]);
        }

        return $this->render('admin/enseigne/2-form.html.twig', [
            'enseigne' => $enseigne,
            'form' => $form,
            'current_step' => 2,
            'current_substep' => 1,
            'availableFields' => FormDefinitions::getFieldDefinitions([$enseigne->getPole()]),
        ]);
    }

    #[Route('/{id}/notifications', name: 'app_admin_enseigne_3_notifications')]
    public function notifications(Request $request, Enseigne $enseigne, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(EnseigneNotificationsType::class, $enseigne);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $enseigne->setFormCompleted(true);
            $entityManager->flush();

            $this->addFlash('success', 'Les modifications ont été enregistrées avec succès.');

            return $this->redirectToRoute('app_admin_enseigne_liste');
        }

        return $this->render('admin/enseigne/3-notifications.html.twig', [
            'enseigne' => $enseigne,
            'form' => $form,
            'current_step' => 3,
            'current_substep' => 1,
        ]);
    }
}
