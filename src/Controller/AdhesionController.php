<?php

namespace App\Controller;

use App\Entity\Enseigne;
use App\Form\AdhesionOptionsType;
use App\Form\AdhesionSepaType;
use App\Form\AdhesionSignatureType;
use App\Model\AdhesionInfosFormModel;
use App\Model\AdhesionModel;
use App\Model\AdhesionSepaFormModel;
use App\Model\AdhesionSignatureFormModel;
use App\Service\DynamicFormBuilder;
use App\Service\FormSessionHandler;
use App\Service\Mailer;
use App\Service\PinHandler;
use App\Service\StepManager;
use App\Service\TestManager;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\RateLimiter\Exception\RateLimitExceededException;
use Symfony\Component\Routing\Annotation\Route;

class AdhesionController extends AbstractController
{
    public function __construct(
        private readonly StepManager $stepManager,
        private readonly DynamicFormBuilder $formBuilder,
        private readonly FormSessionHandler $formSessionHandler,
    ) {
    }

    public function redirectToLastStep(Enseigne $enseigne, AdhesionModel $adhesion, int $currentStep)
    {
        $lastStepUrl = $this->formSessionHandler->denyAccessUnlessGranted($enseigne, $adhesion, $currentStep);
        if (null !== $lastStepUrl) {
            return new RedirectResponse($lastStepUrl);
        }

        return null;
    }

    #[Route('/c/{identifier}', name: 'app_adhesion_summary')]
    public function list(Enseigne $enseigne, Request $request): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();

        $step = 0;
        $this->formSessionHandler->setLastStep($adhesion, $step, true);

        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        return $this->render('adhesion/index.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'stepManager' => $this->stepManager,
        ]);
    }

    #[Route('/c/{identifier}/1', name: 'app_adhesion_step_1')]
    public function step1(Enseigne $enseigne): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();

        $step = 1;
        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        $this->formSessionHandler->setLastStep($adhesion, $step, true);

        return $this->render('adhesion/1-discover.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'stepManager' => $this->stepManager,
        ]);
    }

    #[Route('/c/{identifier}/2', name: 'app_adhesion_step_2')]
    public function step2(Enseigne $enseigne, Request $request): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();

        $step = 2;
        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        $this->formSessionHandler->initOptions($adhesion, $enseigne);

        $form = $this->createForm(AdhesionOptionsType::class, $adhesion);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->formSessionHandler->saveAdhesion($adhesion, $step);

            return $this->redirectToRoute('app_adhesion_step_3', ['identifier' => $enseigne->getIdentifier()]);
        }

        return $this->render('adhesion/2-options.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'form' => $form,
            'stepManager' => $this->stepManager,
        ]);
    }

    #[Route('/c/{identifier}/3', name: 'app_adhesion_step_3')]
    public function step3(Enseigne $enseigne, Request $request): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();
        $step = 3;
        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        if (null === $adhesion->infos) {
            $adhesion->infos = new AdhesionInfosFormModel();
        }

        $form = $this->createFormBuilder($adhesion->infos->dynamicFields)->setMethod('POST');

        $this->formBuilder->buildForm($form, $enseigne);

        $form = $form->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $adhesion->infos->dynamicFields = $form->getData();
            $this->formSessionHandler->saveAdhesion($adhesion, $step);

            return $this->redirectToRoute('app_adhesion_step_4', ['identifier' => $enseigne->getIdentifier()]);
        }

        return $this->render('adhesion/3-infos.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'form' => $form,
            'stepManager' => $this->stepManager,
        ]);
    }

    #[Route('/c/{identifier}/4', name: 'app_adhesion_step_4')]
    public function step4(Enseigne $enseigne, Request $request, PinHandler $pinHandler, LoggerInterface $logger): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();

        $step = 4;
        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        if (null === $adhesion->sepa) {
            $adhesion->sepa = new AdhesionSepaFormModel();
        }

        $form = $this->createForm(AdhesionSepaType::class, $adhesion->sepa, [
            'enseigne' => $enseigne,
        ]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $pinHandler->send($enseigne, $adhesion);
            } catch (RateLimitExceededException $exception) {
                $seconds = $exception->getRetryAfter()->getTimestamp() - time();
                $this->addFlash('warning', sprintf('Veuillez patienter %s secondes avant de pouvoir envoyer un nouveau SMS', $seconds));

                return $this->redirectToRoute('app_adhesion_step_4', ['identifier' => $enseigne->getIdentifier()]);
            } catch (\Throwable $exception) {
                $this->addFlash('danger', 'Une erreur est survenue lors de l\'envoi du SMS. Veuillez réessayer.');

                $logger->error(sprintf("Erreur d'envoi Linkmobility : %s", $exception->getMessage()));

                return $this->redirectToRoute('app_adhesion_step_4', ['identifier' => $enseigne->getIdentifier()]);
            }

            $this->formSessionHandler->saveAdhesion($adhesion, $step);

            return $this->redirectToRoute('app_adhesion_step_5', ['identifier' => $enseigne->getIdentifier()]);
        }

        return $this->render('adhesion/4-sepa.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'form' => $form,
            'stepManager' => $this->stepManager,
            'adhesion' => $adhesion,
        ]);
    }

    #[Route('/c/{identifier}/5', name: 'app_adhesion_step_5')]
    public function step5(Enseigne $enseigne, Request $request, Mailer $mailer, TestManager $testManager): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();

        $step = 5;
        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        if (null === $adhesion->signature) {
            $adhesion->signature = new AdhesionSignatureFormModel();
        }

        $adhesion->signature->signatureDate = new \DateTime();

        $form = $this->createForm(AdhesionSignatureType::class, $adhesion->signature, [
            'enseigne' => $enseigne,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $adhesion->signature->signed = true;
            $this->formSessionHandler->saveAdhesion($adhesion, $step);
            $mailer->sendEmails($enseigne, $adhesion);

            return $this->redirectToRoute('app_adhesion_step_6', ['identifier' => $enseigne->getIdentifier()]);
        }

        $response = new Response();
        if ($testManager->isTesting()) {
            $response->headers->add(['X-ADHESION-PIN' => $adhesion->signature->requiredPin]);
        }

        return $this->render('adhesion/5-signature.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'form' => $form,
            'stepManager' => $this->stepManager,
            'adhesion' => $adhesion,
        ], response: $response);
    }

    #[Route('/c/{identifier}/6', name: 'app_adhesion_step_6')]
    public function step6(Enseigne $enseigne): Response
    {
        $adhesion = $this->formSessionHandler->restoreAdhesion();

        $step = 6;
        $redirectResponse = $this->redirectToLastStep($enseigne, $adhesion, $step);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        $this->formSessionHandler->setLastStep($adhesion, $step, true);

        return $this->render('adhesion/6-end.html.twig', [
            'enseigne' => $enseigne,
            'step' => $step,
            'stepManager' => $this->stepManager,
            'adhesion' => $adhesion,
        ]);
    }
}
