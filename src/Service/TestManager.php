<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;

class TestManager
{
    public const TEST_ENV_COOKIE = 'test_env';

    public function __construct(
        protected RequestStack $requestStack,
        #[Autowire(env: 'APP_SECRET')] protected string $testEnvSecret,
    ) {
    }

    public function isTesting()
    {
        $cookies = $this->requestStack->getCurrentRequest()->cookies;

        return $cookies->has(self::TEST_ENV_COOKIE) && $cookies->get(self::TEST_ENV_COOKIE) === $this->testEnvSecret;
    }
}
