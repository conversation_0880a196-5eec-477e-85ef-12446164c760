<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Entity\Option;
use App\Model\AdhesionModel;
use App\Model\AdhesionOptionsFormModel;
use App\Repository\EnseigneRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;

class FormSessionHandler
{
    public const SESSION_KEY = '__adhesion_form_%s';

    private AdhesionModel $adhesion;

    public function __construct(
        private readonly RequestStack $requestStack,
        private readonly RouterInterface $router,
        private readonly EnseigneRepository $enseigneRepository,
    ) {
    }

    public function getSessionKey(Enseigne $enseigne): string
    {
        return sprintf(self::SESSION_KEY, $enseigne->getId());
    }

    public function getEnseigne(): Enseigne
    {
        return $this->enseigneRepository->findOneByIdentifier($this->requestStack->getCurrentRequest()->attributes->get('identifier'));
        // todo améliorer suite montée de version
        // return $this->requestStack->getCurrentRequest()->attributes->get('enseigne');
    }

    public function createAdhesion(): AdhesionModel
    {
        $adhesion = new AdhesionModel();
        $this->adhesion = $adhesion;

        return $adhesion;
    }

    public function restoreAdhesion(): AdhesionModel
    {
        $session = $this->requestStack->getSession();
        $sessionKey = $this->getSessionKey($this->getEnseigne());
        if ($session->has($sessionKey)) {
            $this->adhesion = unserialize($session->get($sessionKey));
        } else {
            $this->createAdhesion();
        }

        return $this->adhesion;
    }

    public function saveAdhesion(AdhesionModel $adhesion, ?int $step = null): void
    {
        if ($step) {
            $this->setLastStep($adhesion, $step);
        }
        $this->requestStack->getSession()->set($this->getSessionKey($this->getEnseigne()), serialize($adhesion));
    }

    public function initOptions(AdhesionModel $adhesion, Enseigne $enseigne)
    {
        if (null === $adhesion->options) {
            $adhesion->options = new ArrayCollection();
        }

        foreach ($enseigne->getOptions() as $option) {
            $adhesionOption = $adhesion->options->filter(fn (AdhesionOptionsFormModel $ao) => $ao->option->getId() === $option->getId())->first();
            if (false === $adhesionOption) {
                $adhesionOption = new AdhesionOptionsFormModel();
                $adhesionOption->option = $option;
                $adhesionOption->quantity = $option->isIsFree() ? 1 : 0;
                $adhesion->options->add($adhesionOption);
            }
        }

        // Nettoyage des options qui n'existeraient plus
        $optionsIds = $enseigne->getOptions()->map(fn (Option $o) => $o->getId())->toArray();
        foreach ($adhesion->options as $adhesionOption) {
            if (!in_array($adhesionOption->option->getId(), $optionsIds)) {
                $adhesion->options->removeElement($adhesionOption);
            }
        }
    }

    public function getAdhesion(): AdhesionModel
    {
        return $this->adhesion;
    }

    public function setAdhesion(AdhesionModel $adhesion): void
    {
        $this->adhesion = $adhesion;
    }

    public function denyAccessUnlessGranted(Enseigne $enseigne, AdhesionModel $adhesion, int $step): ?string
    {
        // Si on a pas encore commencé le parcours
        // ou si on l'a terminé et que l'on accède à une autre étape que le 6
        // On redirige vers la page d'accueil
        if (-1 === $adhesion->lastStep || (6 !== $step && $adhesion->signature && true === $adhesion->signature->signed)) {
            if ($adhesion->signature && $adhesion->signature->signed) {
                $this->saveAdhesion($this->createAdhesion());
            }

            return $this->router->generate('app_adhesion_summary', ['identifier' => $enseigne->getIdentifier()]);
        }

        if ($step > ($adhesion->lastStep + 1)) {
            return $this->router->generate(
                sprintf('app_adhesion_step_%s', min($adhesion->lastStep + 1, 6)),
                ['identifier' => $enseigne->getIdentifier()]
            );
        }

        return null;
    }

    public function setLastStep(AdhesionModel $adhesion, int $step, bool $save = false): void
    {
        if ($step > $adhesion->lastStep) {
            $adhesion->lastStep = $step;
            if ($save) {
                $this->saveAdhesion($adhesion);
            }
        }
    }
}
