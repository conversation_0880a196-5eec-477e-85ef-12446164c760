<?php

namespace App\Service;

use App\Entity\Enseigne;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Uid\Uuid;

class FileUploader
{
    public function __construct(
        #[Autowire(param: 'targetDirectory')] private readonly string $targetDirectory,
    ) {
    }

    public function handleForm(FormInterface $form, Enseigne $enseigne)
    {
        /** @var UploadedFile $logoFile */
        $logoFile = $form->get('logoFile')->getData();
        if ($logoFile) {
            $enseigne->setLogo($this->upload($logoFile));
        }

        /** @var UploadedFile $contractFile */
        $contractFile = $form->get('contractFile')->getData();
        if ($contractFile) {
            $enseigne->setContract($this->upload($contractFile));
        }
    }

    public function upload(UploadedFile $file)
    {
        $fileName = Uuid::v4()->toRfc4122() . '.' . $file->guessExtension();

        try {
            $file->move($this->targetDirectory, $fileName);
        } catch (FileException $e) {
            // @todo ajouter erreur dans le formulaire
            throw $e;
        }

        return $fileName;
    }
}
