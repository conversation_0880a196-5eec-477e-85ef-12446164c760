<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Model\AdhesionModel;
use App\Model\AdhesionSignatureFormModel;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Notifier\Message\SmsMessage;
use Symfony\Component\Notifier\TexterInterface;
use Symfony\Component\RateLimiter\Exception\RateLimitExceededException;
use Symfony\Component\RateLimiter\RateLimiterFactory;

class PinHandler
{
    public function __construct(
        private readonly TexterInterface $texter,
        private readonly RequestStack $requestStack,
        private readonly RateLimiterFactory $smsSendLimiter,
        private readonly TestManager $testManager,
    ) {
    }

    public function send(Enseigne $enseigne, AdhesionModel $adhesionModel)
    {
        $limiter = $this->smsSendLimiter->create($this->requestStack->getCurrentRequest()->getSession()->getId());
        $limit = $limiter->consume();
        if (!$limit->isAccepted()) {
            throw new RateLimitExceededException($limit);
        }

        $pin = self::generatePin();
        if (!isset($adhesionModel->signature)) {
            $adhesionModel->signature = new AdhesionSignatureFormModel();
        }
        $adhesionModel->signature->requiredPin = $pin;
        $adhesionModel->signature->pinSentDate = new \DateTime();

        if ($this->testManager->isTesting()) {
            return true;
        }

        $sms = new SmsMessage(
            $adhesionModel->sepa->signaturePhone,
            sprintf('Pour valider électroniquement votre adhésion au programme %s, renseignez le code : %s', $enseigne->getName(), $pin)
        );

        return $this->texter->send($sms);
    }

    public static function generatePin($length = 4)
    {
        return str_pad(random_int(0, 10 ** $length - 1), $length, '0', STR_PAD_LEFT);
    }

    public static function isAlreadySent(AdhesionModel $adhesionModel)
    {
        return isset($adhesionModel->signature->requiredPin) && null !== $adhesionModel->signature->requiredPin;
    }
}
