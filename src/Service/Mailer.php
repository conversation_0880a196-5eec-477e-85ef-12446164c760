<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Model\AdhesionModel;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class Mailer
{
    private ?string $contractPath;

    public function __construct(
        private readonly MailerInterface $mailer,
        private readonly CustomTemplater $templater,
        private readonly PdfGenerator $pdfGenerator,
        private readonly RequestStack $requestStack,
    ) {
        $this->contractPath = null;
    }

    public function buildEmail(Enseigne $enseigne)
    {
        $email = (new TemplatedEmail())
            ->from(new Address(sprintf('noreply@%s', $this->requestStack->getCurrentRequest()->getHost())))
            ->replyTo(new Address($enseigne->getEmailSenderEmail(), $enseigne->getEmailSenderName()));

        return $email;
    }

    public function attachContract(Email $email, Enseigne $enseigne, AdhesionModel $adhesion): void
    {
        if (null === $this->contractPath) {
            $this->contractPath = $this->pdfGenerator->getFullPdf($enseigne, $adhesion);
        }
        $email->attachFromPath($this->contractPath, sprintf('contrat-pharmupp-%s.pdf', $enseigne->getContractName($adhesion)));
    }

    public function sendEmails(Enseigne $enseigne, AdhesionModel $adhesion): void
    {
        if ($enseigne->isEmailEnseigneEnabled()) {
            $this->sendEnseigneEmail($enseigne, $adhesion);
        }
        if ($enseigne->isEmailAquitemEnabled()) {
            $this->sendAquitemEmail($enseigne, $adhesion);
        }
        if ($enseigne->isEmailSubscriberEnabled()) {
            $this->sendSubscriberEmail($enseigne, $adhesion);
        }
    }

    /**
     * E-mail envoyé à destination du siège de l'enseigne.
     */
    public function sendEnseigneEmail(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        $recipients = Address::createArray(explode(',', (string) $enseigne->getEmailEnseigneRecipientEmail()));

        $email = $this->buildEmail($enseigne)
            ->to(...$recipients)
            ->subject($this->templater->render($enseigne->getEmailEnseigneSubject(), $enseigne, $adhesion))
            ->text($this->templater->render($enseigne->getEmailEnseigneContent(), $enseigne, $adhesion));

        if ($enseigne->isEmailEnseigneAttachContract()) {
            $this->attachContract($email, $enseigne, $adhesion);
        }

        $this->mailer->send($email);
    }

    /**
     * E-mail envoyé à destination de Aquitem.
     */
    public function sendAquitemEmail(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        $recipients = Address::createArray(explode(',', (string) $enseigne->getEmailAquitemRecipientEmail()));

        $email = $this->buildEmail($enseigne)
            ->to(...$recipients)
            ->subject($this->templater->render($enseigne->getEmailAquitemSubject(), $enseigne, $adhesion))
            ->text($this->templater->render($enseigne->getEmailAquitemContent(), $enseigne, $adhesion));

        if ($enseigne->isEmailAquitemAttachContract()) {
            $this->attachContract($email, $enseigne, $adhesion);
        }

        $this->mailer->send($email);
    }

    /**
     * E-mail envoyé à destination du partenaire.
     */
    public function sendSubscriberEmail(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        $email = $this->buildEmail($enseigne)
            ->to(new Address($adhesion->infos->getRequiredValue($enseigne, 'managerEmail')))
            ->subject($this->templater->render($enseigne->getEmailSubscriberSubject(), $enseigne, $adhesion))
            ->text($this->templater->render($enseigne->getEmailSubscriberContent(), $enseigne, $adhesion));

        if ($enseigne->isEmailSubscriberAttachContract()) {
            $this->attachContract($email, $enseigne, $adhesion);
        }

        $this->mailer->send($email);
    }
}
