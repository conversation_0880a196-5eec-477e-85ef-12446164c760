<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Model\AdhesionModel;
use mikeha<PERSON>l\pdftk\Pdf;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\WebpackEncoreBundle\Asset\EntrypointLookupInterface;
use Twig\Environment;

class PdfGenerator
{
    private readonly Filesystem $filesystem;
    private readonly string $sepaBackgroundPath;

    public const FOOTER_HEIGHT = '2.25cm';

    public function __construct(
        private readonly \Knp\Snappy\Pdf $snappy,
        private readonly Environment $twig,
        ParameterBagInterface $parameterBag,
        #[Autowire(param: 'targetDirectory')] private readonly string $uploadDir,
        private readonly EntrypointLookupInterface $entrypointLookup,
    ) {
        $this->filesystem = new Filesystem();
        $projectDir = $parameterBag->get('kernel.project_dir');
        $this->sepaBackgroundPath = sprintf('%s/pdf/sepa.pdf', $projectDir);
    }

    public function renderPdf(Enseigne $enseigne, string $html)
    {
        $this->entrypointLookup->reset();
        $output = $this->snappy->getOutputFromHtml($html, [
            'footer-html' => $this->getFooterHtml($enseigne),
            'margin-bottom' => self::FOOTER_HEIGHT,
            'margin-left' => 0,
            'margin-right' => 0,
        ]);
        $this->snappy->removeTemporaryFiles();
        $this->snappy->resetOptions();
        $this->entrypointLookup->reset();

        return $output;
    }

    public function savePdfData(string $blob)
    {
        $path = $this->getTempnam();
        file_put_contents($path, $blob);

        return $path;
    }

    public function getFooterHtml(Enseigne $enseigne)
    {
        return $this->twig->render('pdf/footer.html.twig', [
            'enseigne' => $enseigne,
        ]);
    }

    public function getSepaHtml(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        return $this->twig->render('pdf/sepa.html.twig', [
            'enseigne' => $enseigne,
            'adhesion' => $adhesion,
        ]);
    }

    public function getContractHtml(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        return $this->twig->render('pdf/contract.html.twig', [
            'enseigne' => $enseigne,
            'adhesion' => $adhesion,
        ]);
    }

    public function getSepaPdf(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        $sepaPath = $this->savePdfData($this->renderPdf($enseigne, $this->getSepaHtml($enseigne, $adhesion)));
        $pdf = (new Pdf($sepaPath))->background($this->sepaBackgroundPath);
        $path = $this->getTempnam();
        $pdf->saveAs($path);

        return $path;
    }

    public function getContractPdf(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        return $this->renderPdf($enseigne, $this->getContractHtml($enseigne, $adhesion));
    }

    public function getFullPdf(Enseigne $enseigne, AdhesionModel $adhesion)
    {
        $pdfConfig = [
            // Contrat uploadé au niveau du formulaire de l'enseigne
            'A' => sprintf('%s/%s', $this->uploadDir, $enseigne->getContract()),
            // Suite du contrat généré en HTML
            'B' => $this->savePdfData($this->getContractPdf($enseigne, $adhesion)),
        ];

        if ($enseigne->getAskForSepa()) {
            // Mandate de prélèvement SEPA qui a besoin d'un background particulier
            $pdfConfig['C'] = $this->getSepaPdf($enseigne, $adhesion);
        }

        $pdf = new Pdf($pdfConfig);

        $resultPath = $this->getTempnam();

        $result = $pdf
            ->cat()
            // On ajoute le contrat de l'enseigne
            ->cat(1, 'end', 'A')
            // On retire la dernière page du contrat généré
            ->cat(1, 'r2', 'B');

        if ($enseigne->getAskForSepa()) {
            // On insère le mandat SEPA
            $result->cat(1, 'end', 'C');
        }

        // On ajoute la dernière page du contrat généré
        $result->cat('r1', 'end', 'B')
            ->saveAs($resultPath);

        if (false === $result) {
            throw new \Exception("Une erreur s'est produite lors de la génération du PDF");
        }

        return $resultPath;
    }

    public function getTempnam(): string
    {
        return $this->filesystem->tempnam('/tmp', 'pdf_', '.pdf');
    }
}
