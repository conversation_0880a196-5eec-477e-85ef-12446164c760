<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Model\AdhesionInfosFormModel;
use App\Model\AdhesionModel;
use App\Model\AdhesionOptionsFormModel;
use App\Model\AdhesionSepaFormModel;
use App\Model\AdhesionSignatureFormModel;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class DataMocker
{
    public static function mockAdhesion(Enseigne $enseigne): AdhesionModel
    {
        $adhesion = new AdhesionModel();

        $adhesion->infos = self::mockInfos();
        $adhesion->signature = self::mockSignature();
        $adhesion->sepa = self::mockSepa();
        $adhesion->options = self::mockOptions($enseigne);

        return $adhesion;
    }

    public static function mockInfos(): AdhesionInfosFormModel
    {
        $infos = new AdhesionInfosFormModel();
        $infos->dynamicFields = [
            'informations-generales-votre-numero-finess' => '122222222',
            'informations-generales-votre-code-id-pharm-upp' => 'FN0012AB',
            'informations-generales-siret' => '12345678910123',
            'informations-generales-votre-statut' => 'Essentiel',
            'informations-generales-raison-sociale' => 'Thomas Choquet',
            'informations-generales-date-souhaitee-d-entree-dans-le-programme' => new \DateTime(),
            'coordonnees-votre-libelle-de-pharmacie-ex-pharmacie-de-la-poste' => 'Pharmacie de la poste',
            'coordonnees-code-postal' => '33110',
            'coordonnees-adresse' => '375 avenue de Tivoli',
            'coordonnees-complement-d-adresse' => 'Batiment Aquitem',
            'coordonnees-ville' => 'Saint-Remy-en-Bouzemont',
            'coordonnees-tel-fixe' => '+33123456789',
            'responsable-de-la-pharmacie-civilite' => 'M.',
            'responsable-de-la-pharmacie-prenom' => 'Thomas',
            'responsable-de-la-pharmacie-nom' => 'Choquet',
            'responsable-de-la-pharmacie-tel-portable' => '+33612345678',
            'responsable-de-la-pharmacie-adresse-e-mail' => '<EMAIL>',
            'responsable-de-la-comptabilite-civilite' => 'M.',
            'responsable-de-la-comptabilite-prenom' => 'Thomas',
            'responsable-de-la-comptabilite-nom' => 'Choquet',
            'responsable-de-la-comptabilite-tel' => '+33612345678',
            'esponsable-de-la-comptabilite-adresse-e-mail-de-reception-des-factures' => '<EMAIL>',
            'votre-prestataire-informatique-votre-prestataire' => 'LGPI/ Pharmagest',
            'votre-prestataire-informatique-autre' => '',
        ];

        return $infos;
    }

    public static function mockSignature(): AdhesionSignatureFormModel
    {
        $signature = new AdhesionSignatureFormModel();
        $signature->signatureData = 'data:image/png;base64,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';
        $signature->cgvAccepted = true;
        $signature->debitAccepted = true;
        $signature->sepaAccepted = true;
        $signature->signatureDate = (new \DateTime('2022-11-01 13:37:13'));
        $signature->pinSentDate = (new \DateTime('2022-11-01 13:32:37'));
        $signature->signed = true;

        return $signature;
    }

    public static function mockSepa(): AdhesionSepaFormModel
    {
        $sepa = new AdhesionSepaFormModel();
        $sepa->iban = '***************************';
        $sepa->bic = 'AGRIFRPP';
        $sepa->sepaName = 'Enseigne';
        $sepa->address = '123 rue de la paix';
        $sepa->address2 = '';
        $sepa->zipCode = '75000';
        $sepa->city = 'Paris';
        $sepa->country = 'FR';
        $sepa->signaturePhone = '+33123456789';

        return $sepa;
    }

    public static function mockOptions(Enseigne $enseigne): Collection
    {
        $options = [];

        foreach ($enseigne->getOptions() as $index => $option) {
            $a = new AdhesionOptionsFormModel();
            $a->option = $option;
            $a->quantity = $index + 1;
            $options[] = $a;
        }

        return new ArrayCollection($options);
    }
}
