<?php

namespace App\Service;

use App\Entity\Enseigne;

class StepManager
{
    public function getSteps(Enseigne $enseigne): array
    {
        $steps = [
            'Découvrir les prestations et les conditions tarifaires',
            'Sélectionnez vos options et packs',
            'Renseignez vos informations',
        ];

        if ($enseigne->getAskForSepa()) {
            $steps[] = 'Demande de prélèvement SEPA';
        } else {
            $steps[] = 'Validation par SMS';
        }

        $steps[] = "Signature numérique du dossier d'adhésion et validation par SMS";

        return $steps;
    }

    public function getStep(Enseigne $enseigne, int $index): string
    {
        return $this->getSteps($enseigne)[$index - 1];
    }
}
