<?php

namespace App\Service\Notifier;

use App\Entity\Enseigne;
use App\Repository\EnseigneRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Notifier\Exception\UnsupportedSchemeException;
use Symfony\Component\Notifier\Transport\AbstractTransportFactory;
use Symfony\Component\Notifier\Transport\Dsn;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class LinkMobilitySmsTransportFactory extends AbstractTransportFactory
{
    public function __construct(
        private readonly RequestStack $requestStack,
        #[Autowire(env: 'LINKMOBILITY_NAME')] private readonly string $linkMobilityName,
        ?EventDispatcherInterface $dispatcher = null,
        ?HttpClientInterface $client = null,
        private readonly EnseigneRepository $enseigneRepository,
    ) {
        parent::__construct($dispatcher, $client);
    }

    public function create(Dsn $dsn): LinkMobilitySmsTransport
    {
        $scheme = $dsn->getScheme();

        if ('linkmobility-sms' !== $scheme) {
            throw new UnsupportedSchemeException($dsn, 'linkmobility-sms', $this->getSupportedSchemes());
        }

        $user = $this->getUser($dsn);
        $from = $dsn->getRequiredOption('from');
        $host = 'default' === $dsn->getHost() ? null : $dsn->getHost();
        $port = $dsn->getPort();
        $enseigne = $this->enseigneRepository->findOneByIdentifier($this->requestStack->getCurrentRequest()->attributes->get('identifier'));

        $accp = null;
        if ($enseigne instanceof Enseigne) {
            $accp = $enseigne->getLinkMobilityAccp();
        }

        return (new LinkMobilitySmsTransport($user, $accp, $from, $this->client, $this->dispatcher))->setHost($host)->setPort($port)->setName($this->linkMobilityName);
    }

    protected function getSupportedSchemes(): array
    {
        return ['linkmobility-sms'];
    }
}
