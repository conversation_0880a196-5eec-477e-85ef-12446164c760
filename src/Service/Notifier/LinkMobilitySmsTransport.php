<?php

namespace App\Service\Notifier;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Notifier\Exception\TransportException;
use Symfony\Component\Notifier\Exception\UnsupportedMessageTypeException;
use Symfony\Component\Notifier\Message\MessageInterface;
use Symfony\Component\Notifier\Message\SentMessage;
use Symfony\Component\Notifier\Message\SmsMessage;
use Symfony\Component\Notifier\Transport\AbstractTransport;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class LinkMobilitySmsTransport extends AbstractTransport
{
    protected const HOST = 'restv2.netmessage.com';
    private ?string $name = null;

    public function __construct(
        private readonly string $apiKey,
        private string $accp,
        private string $from,
        ?HttpClientInterface $client = null,
        ?EventDispatcherInterface $dispatcher = null,
    ) {
        parent::__construct($client, $dispatcher);
    }

    public function setAccp(string $accp): LinkMobilitySmsTransport
    {
        $this->accp = $accp;

        return $this;
    }

    public function setFrom(string $from): LinkMobilitySmsTransport
    {
        $this->from = $from;

        return $this;
    }

    public function setName(?string $name): LinkMobilitySmsTransport
    {
        $this->name = $name;

        return $this;
    }

    public function __toString(): string
    {
        return sprintf('linkmobility-sms://%s?from=%s', $this->getEndpoint(), $this->from);
    }

    public function supports(MessageInterface $message): bool
    {
        return $message instanceof SmsMessage;
    }

    public function doSend(MessageInterface $message): SentMessage
    {
        if (!$message instanceof SmsMessage) {
            throw new UnsupportedMessageTypeException(self::class, SmsMessage::class, $message);
        }

        $url = 'https://' . $this->getEndpoint() . '/smsdirect/send';

        $payload = [
            'apikey' => $this->apiKey,
            'accp' => $this->accp,
            'sender' => $this->from,
            'num' => $message->getPhone(),
            'msg' => $message->getSubject(),
            'name' => $this->name,
        ];

        $response = $this->client->request('GET', $url, [
            'query' => $payload,
        ]);

        if (200 !== $response->getStatusCode()) {
            $content = $response->toArray(false);
            throw new TransportException(sprintf('Unable to send the SMS: "%s"', $content), $response);
        }

        return new SentMessage($message, (string) $this);
    }
}
