<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Model\AdhesionModel;

class CustomTemplater
{
    /**
     * @param array<string> $exclusions Liste de champs à exclure par ex ['{numero-contrat}']
     *
     * @return array<string>
     */
    public static function getAvailableTags(?Enseigne $enseigne = null, array $exclusions = []): array
    {
        $tags = $enseigne->getAvailableTags();

        $tags[] = '{numero-contrat}';
        $tags[] = '{signature-date}';

        if ($enseigne) {
            foreach ($enseigne->getOptions() as $option) {
                $tags[] = sprintf('{option-label-%s}', $option->getId());
                $tags[] = sprintf('{option-qtt-%s}', $option->getId());
            }
        }

        return array_diff($tags, $exclusions);
    }

    public function render(string $template, Enseigne $enseigne, AdhesionModel $adhesion)
    {
        $availableTags = $this->getAvailableTags($enseigne);

        $tags = [];

        foreach ($availableTags as $tag) {
            $fieldName = str_replace(['{', '}'], '', $tag);
            if ($adhesion->infos->hasDynamicValue($fieldName)) {
                $dynamicValue = $adhesion->infos->getDynamicValueFormatted($fieldName);
                $tags[$tag] = $dynamicValue ?? '';
            }
        }

        if (str_contains($template, '{numero-contrat}')) {
            $tags['{numero-contrat}'] = $enseigne->getContractName($adhesion) ?? '';
        }
        if (str_contains($template, '{signature-date}')) {
            $tags['{signature-date}'] = $adhesion->signature->signatureDate?->format('YmdHis') ?? '';
        }

        if ($adhesion->options) {
            foreach ($adhesion->options as $adhesionOption) {
                $tags[sprintf('{option-label-%s}', $adhesionOption->option->getId())] = $adhesionOption->option->getName();
                $tags[sprintf('{option-qtt-%s}', $adhesionOption->option->getId())] = $adhesionOption->quantity;
            }
        }

        foreach ($tags as $tag => $value) {
            $template = str_replace($tag, $value, $template);
        }

        return $template;
    }
}
