<?php

namespace App\Repository;

use App\Entity\ItProvider;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ItProvider>
 *
 * @method ItProvider|null find($id, $lockMode = null, $lockVersion = null)
 * @method ItProvider|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItProvider[]    findAll()
 * @method ItProvider[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ItProviderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ItProvider::class);
    }

    public function save(ItProvider $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ItProvider $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @return string[]
     */
    public function findNames(): array
    {
        $result = $this->createQueryBuilder('i')
            ->select('i.name')
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'name');
    }
}
