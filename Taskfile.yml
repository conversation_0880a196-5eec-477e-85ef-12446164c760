# https://taskfile.dev

version: '3'

vars:
  GITLAB_PROJECT: 'dev-projets-aquitem/demat-adhesion'
  PREPROD_SERVER: 'aquitem-preprod'
  PREPROD_SERVICE: 'web-4462_front_web'
  PROD_SERVER: 'aquitem-prod'
  PROD_SERVICE: 'web-4463_front_web'

includes:
  docker: https://gitlab.alienor.net/-/snippets/11/raw/main/Taskfile.yml

tasks:
  default:
    cmds:
      - task: docker:exec
    silent: true

  lint:
    desc: Lint php and twig files
    cmds:
      - 'docker compose exec -e PHP_CS_FIXER_IGNORE_ENV=1 php sudo -Eu www-data php vendor/bin/php-cs-fixer fix'
      - 'docker compose exec php sudo -Eu www-data php vendor/bin/twig-cs-fixer lint --fix templates'
  test:
    desc: Run all tests
    cmds:
      - '{{.DOCKER_PHP}} bin/phpunit'
  reset-db:
      desc: Reset database
      cmds:
          - '{{.DOCKER_SYMFONY}} doctrine:database:drop --force --if-exists'
          - '{{.DOCKER_SYMFONY}} doctrine:database:create'
          - '{{.DOCKER_SYMFONY}} doctrine:migration:migrate --no-interaction'
          - '{{.DOCKER_SYMFONY}} doctrine:fixtures:load --no-interaction --group=default'
  reset-db-test:
    desc: Create and reset test database
    cmds:
      - '{{.DOCKER_COMP}} exec database mysql -uroot -proot -e "DROP DATABASE IF EXISTS anetdb_test;"'
      - '{{.DOCKER_COMP}} exec database mysql -uroot -proot -e "CREATE DATABASE anetdb_test;"'
      - |
        {{.DOCKER_COMP}} exec database mysql -uroot -proot -e "GRANT ALL PRIVILEGES ON anetdb_test.* TO 'userdb'@'%'; FLUSH PRIVILEGES;"
      - '{{.DOCKER_SYMFONY}} doctrine:migration:migrate --no-interaction --env=test'
      - '{{.DOCKER_SYMFONY}} doctrine:fixtures:load --no-interaction --env=test --group=default'
