#syntax=docker/dockerfile:1.4

# Base FrankenPHP image
FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_base:8.4-frankenphp AS frankenphp_base

RUN mkdir -p /usr/share/man/man1 \
    && apt-get update \
    && apt-get install -y libxrender1 libfontconfig1 libssl-dev pdftk poppler-utils exim4 \
    && rm -rf /var/lib/apt/lists/*

RUN echo "deb http://deb.debian.org/debian bullseye main" > /etc/apt/sources.list.d/bullseye.list \
    && apt-get update && apt-get install -y \
    libssl1.1=1.1.1* \
    && rm -rf /var/lib/apt/lists/*

RUN set -eux; \
	install-php-extensions \
		pdo_mysql \
	;

# ~~~~~ CONFIGURE EXIM4 ~~~~~~~

RUN echo "dc_eximconfig_configtype='smarthost'\n\
dc_local_interfaces='127.0.0.1 ; ::1'\n\
dc_smarthost='smtp.alienor.net'\n\
CFILEMODE='644'" > /etc/exim4/update-exim4.conf.conf

RUN service exim4 restart

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/conf.d/
COPY --link frankenphp/Caddyfile /etc/caddy/Caddyfile

HEALTHCHECK --start-period=60s CMD echo "healthcheck" || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]

FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_dev:8.4-frankenphp AS frankenphp_dev

RUN mkdir -p /usr/share/man/man1 \
    && apt-get update \
    && apt-get install -y libxrender1 libfontconfig1 libssl-dev pdftk poppler-utils exim4 \
    && rm -rf /var/lib/apt/lists/*

RUN echo "deb http://deb.debian.org/debian bullseye main" > /etc/apt/sources.list.d/bullseye.list \
    && apt-get update && apt-get install -y \
    libssl1.1=1.1.1* \
    && rm -rf /var/lib/apt/lists/*

RUN set -eux; \
	install-php-extensions \
		pdo_mysql \
	;

ENV NODE_MAJOR=22

RUN sudo apt-get update && \
    sudo apt-get install -y ca-certificates curl gnupg && \
    sudo mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | sudo tee /etc/apt/sources.list.d/nodesource.list && \
    curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add - && \
    echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list && \
    sudo apt-get update && \
    sudo apt-get install -y nodejs yarn && \
    mkdir -p /var/www/.cache && \
    chown www-data:www-data /var/www/.cache && \
    touch /var/www/.yarnrc && \
    chown www-data:www-data /var/www/.yarnrc

# ~~~~~ CONFIGURE EXIM4 ~~~~~~~

RUN echo "dc_eximconfig_configtype='smarthost'\n\
dc_local_interfaces='127.0.0.1 ; ::1'\n\
dc_smarthost='smtp.alienor.net'\n\
CFILEMODE='644'" > /etc/exim4/update-exim4.conf.conf

RUN service exim4 restart

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/conf.d/
COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
COPY --link frankenphp/Caddyfile /etc/caddy/Caddyfile
COPY --link frankenphp/conf.d/20-app.dev.ini $PHP_INI_DIR/conf.d/

ENTRYPOINT ["docker-entrypoint"]

HEALTHCHECK --start-period=60s CMD echo "healthcheck" || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]

FROM frankenphp_base AS frankenphp_prod

ENV NODE_MAJOR=22

RUN sudo apt-get update && \
    sudo apt-get install -y ca-certificates curl gnupg && \
    sudo mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | sudo tee /etc/apt/sources.list.d/nodesource.list && \
    curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add - && \
    echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list && \
    sudo apt-get update && \
    sudo apt-get install -y nodejs yarn && \
    mkdir -p /var/www/.cache && \
    chown www-data:www-data /var/www/.cache && \
    touch /var/www/.yarnrc && \
    chown www-data:www-data /var/www/.yarnrc

ENV APP_ENV=prod
#ENV FRANKENPHP_CONFIG="import worker.Caddyfile"

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/conf.d/
COPY --link frankenphp/worker.Caddyfile /etc/caddy/worker.Caddyfile

# prevent the reinstallation of vendors at every changes in the source code
COPY --link composer.* symfony.* package.json yarn.* ./
RUN set -eux; \
    composer install --no-cache --prefer-dist --no-autoloader --no-scripts --no-progress

ARG PDFJS_LICENSE_KEY
ENV PDFJS_LICENSE_KEY=$PDFJS_LICENSE_KEY

COPY --link package.json yarn.* ./
RUN yarn install --frozen-lockfile --ignore-scripts

# copy sources
COPY --link . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
    mkdir -p var/cache var/log; \
    composer dump-autoload --classmap-authoritative; \
    composer run-script post-install-cmd; \
    chmod +x bin/console; sync;

RUN yarn encore production

COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
ENTRYPOINT ["docker-entrypoint"]
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile" ]
