# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=
TRUSTED_HOSTS='^(localhost|adhesion\.localhost)$'
TRUSTED_PROXIES=127.0.0.1,REM<PERSON><PERSON>_ADDR,localhost
###< symfony/framework-bundle ###

###> symfony/webapp-pack ###
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/webapp-pack ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
DATABASE_URL="mysql://userdb:passdb@database:3306/anetdb?serverVersion=10.4.7-MariaDB"
###< doctrine/doctrine-bundle ###

###> symfony/mailer ###
MAILER_DSN=smtp://mailer:1025
#MAILER_DSN="sendmail://localhost?command=/usr/sbin/exim4%20-t"
###< symfony/mailer ###

###> symfony/fake-sms-notifier ###
FAKE_SMS_DSN=fakesms+logger://default
LINKMOBILITY_DSN=linkmobility-sms://API_KEY@default?from=AQUITEM
LINKMOBILITY_NAME="AQUITEM - DEMAT B2B - CODE PIN"
###< symfony/fake-sms-notifier ###

###> karser/karser-recaptcha3-bundle ###
# Get your API key and secret from https://g.co/recaptcha/v3
RECAPTCHA3_KEY=my_site_key
RECAPTCHA3_SECRET=my_secret
RECAPTCHA3_ENABLED=true
###< karser/karser-recaptcha3-bundle ###

###> knplabs/knp-snappy-bundle ###
WKHTMLTOPDF_PATH=%kernel.project_dir%/vendor/silvertipsoftware/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64
###< knplabs/knp-snappy-bundle ###

###> symfony/lock ###
LOCK_DSN=flock
###< symfony/lock ###

###> custom ###
PDFJS_LICENSE_KEY=
SERVER_NAME="adhesion.localhost, localhost"
TASK_X_REMOTE_TASKFILES=1
MAILER_DEV_RECIPIENT='<EMAIL>'
###< custom ###